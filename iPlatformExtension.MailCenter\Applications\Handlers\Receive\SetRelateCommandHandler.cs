﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.Receive;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.MailCenterRepository.Interface;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Receive
{
    /// <summary>
    /// 设置关联关系处理者
    /// </summary>
    internal sealed class SetRelateCommandHandler(IMailCorrelativeRepository mailCorrelativeRepository, IHttpContextAccessor content) : IRequestHandler<SetRelateCommand>
    {
        public async Task Handle(SetRelateCommand request, CancellationToken cancellationToken)
        {
            var userId = content.HttpContext?.User.GetUserID() ?? string.Empty;
            var objList = await mailCorrelativeRepository.Where(it => it.MailId == request.MailId && request.RelateIdList.Contains(it.ObjId)).ToListAsync(it => it.ObjId, cancellationToken);

            var mailCorrelatives = request.RelateIdList.Except(objList).ToList().Select(it => new MailCorrelative
            {
                Id = Guid.NewGuid().ToString(),
                CorrelateType = request.CorrelateType,
                CreateBy = userId,
                CreateTime = DateTime.Now,
                MailId = request.MailId,
                ObjId = it
            });
            await mailCorrelativeRepository.InsertAsync(mailCorrelatives, cancellationToken);
        }
    }
}

