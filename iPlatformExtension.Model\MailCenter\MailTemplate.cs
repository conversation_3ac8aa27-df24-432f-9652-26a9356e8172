﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "mail_template", DisableSyncStructure = true)]
	public partial class MailTemplate {

		/// <summary>
		/// 模板主键ID
		/// </summary>
		[Column(Name = "template_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string TemplateId { get; set; }

		/// <summary>
		/// 邮件模板内容
		/// </summary>
		[Column(Name = "body", StringLength = -2)]
		public string Body { get; set; }

		/// <summary>
		/// 创建用户
		/// </summary>
		[Column(Name = "create_by", StringLength = 50)]
		public string CreateBy { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[Column(Name = "create_time", DbType = "datetime")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 是否有效
		/// </summary>
		[Column(Name = "is_enabled", DbType = "tinyint")]
		public sbyte? IsEnabled { get; set; }

		/// <summary>
		/// 模板名称
		/// </summary>
		[Column(Name = "name", StringLength = 50)]
		public string Name { get; set; }

		/// <summary>
		/// 脚本
		/// </summary>
		[Column(Name = "sql2", StringLength = -1)]
		public string Sql2 { get; set; }

		/// <summary>
		/// 邮件标题
		/// </summary>
		[Column(Name = "title", StringLength = -1)]
		public string Title { get; set; }

		/// <summary>
		/// 更新用户
		/// </summary>
		[Column(Name = "update_by", StringLength = 50)]
		public string UpdateBy { get; set; }

		/// <summary>
		/// 更新时间
		/// </summary>
		[Column(Name = "update_time", DbType = "datetime")]
		public DateTime? UpdateTime { get; set; }

	}

}
