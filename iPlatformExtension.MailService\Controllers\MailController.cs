﻿using Hangfire.Common;
using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Clients.WxWork;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Encryption;
using iPlatformExtension.Common.Interceptor;
using iPlatformExtension.MailService.Applications.Commands;
using iPlatformExtension.MailService.Applications.Queries;
using iPlatformExtension.MailService.Extensions;
using iPlatformExtension.MailService.HostedService;
using iPlatformExtension.MailService.Infrastructure;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.MailCenter;
using MailKit;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.StackExchangeRedis;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using System.Threading;

namespace iPlatformExtension.MailService.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class MailController(ILogger<MailController> logger, IConfiguration configuration, IMediator mediator, DefaultRedisCache redisCache, MailTool mailTool,
        IWxWorkClient wxWorkClientExtension
        ) : ControllerBase
    {

        /// <summary>
        ///  测试
        /// </summary>
        /// <returns></returns>
        [HttpGet("test")]
        public async Task<int> testAsync(CancellationToken cancellationToken)
        {

            await wxWorkClientExtension.SentRobotMessageAsync($"邮箱启动失败，确认最近是否有修改密码，请及时处理。", "MailReceive");

            var receiveMaillst = await mediator.Send(new MailHostQuery(false), cancellationToken);
            if (receiveMaillst.Count() == 0)
            {
                return 0;
            }
            else
            {
                return receiveMaillst.Count();
            }
        }

        /// <summary>
        ///  测试2
        /// </summary>
        /// <returns></returns>
        [HttpGet("test2")]
        public async Task<int> test2(CancellationToken cancellationToken)
        {

            throw new ApplicationException("程序错误");
        }



        /// <summary>
        ///  获取同步中邮件
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetMailList")]
        public string GetMailList()
        {
            if (mailTool.TryGetRecord().Count() == 0)
            {
                return "0";
            }
            return string.Join(",", mailTool.TryGetRecord());
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet("MaiReceive")]
        public async Task<IActionResult> MaiReceive(int repeatLimit = 100, CancellationToken cancellationToken = default)
        {
            logger.LogInformation("公共邮件接收器启动");
            try
            {
                var receiveMaillst = await mediator.Send(new MailHostQuery(false), cancellationToken);

                await Parallel.ForEachAsync(receiveMaillst, async (host, ct) =>
                {
                    if (mailTool.TryGetRecord().TryAdd(host.Account.ToString(), Thread.CurrentThread.ManagedThreadId.ToString()))
                    {
                        try
                        {
                            Console.WriteLine($"{host.Account}进入");
                            var receiveConfig = new Applications.Models.MailConfigDto
                            {
                                HostId = host.HostId,
                                ImapHost = host.ImapHost,
                                ImapPort = host.ImapPort,
                                SenderAccount = host.Account,
                                SenderPassword = DESHelper.DESDecrypt(host.Password),
                                ShowName = host.ShowName,
                                IsPrivate = host.IsPrivate
                            };
                            if (host.ImapHost.Contains("imap"))
                            {
                                await mediator.Send(new ReceiveMailCommand(receiveConfig, repeatLimit), ct);
                            }
                            else
                            {
                                await mediator.Send(new ReceiveMailByPopCommand(receiveConfig, repeatLimit), ct);
                            }
                            Thread.Sleep(200);
                        }
                        catch (Exception ex)
                        {
                            mailTool.TryGetRecord().TryRemove(host.Account.ToString(), out string v);
                            logger.LogError(ex, $"{host.Account}邮箱异常");
                        }
                    }
                });


            }
            catch (Exception ex)
            {
                logger.LogError(ex, "邮件接收异常,请检查程序运行是否正确.");
                throw;
            }
            return Ok("启动成功!");
        }



        /// <summary>
        /// 个人邮件接收器
        /// </summary>
        /// <returns></returns>
        [HttpGet("MaiReceiveByPersonal")]
        public async Task<IActionResult> MaiReceiveByPersonal(int repeatLimit = 10, CancellationToken cancellationToken = default)
        {
            logger.LogInformation("个人邮件接收器启动");
            try
            {
                ParallelOptions po = new ParallelOptions
                {
                    MaxDegreeOfParallelism = 5
                };
                var receiveMaillst = await mediator.Send(new MailHostQuery(true));

                await Parallel.ForEachAsync(receiveMaillst, po, async (host, ct) =>
                {
                    if (mailTool.TryGetRecord().TryAdd(host.Account.ToString(), Thread.CurrentThread.ManagedThreadId.ToString()))
                    {
                        try
                        {
                            var receiveConfig = new Applications.Models.MailConfigDto
                            {
                                HostId = host.HostId,
                                ImapHost = host.ImapHost,
                                ImapPort = host.ImapPort,
                                SenderAccount = host.Account,
                                SenderPassword = DESHelper.DESDecrypt(host.Password),
                                ShowName = host.ShowName,
                                IsPrivate = host.IsPrivate
                            };
                            if (host.ImapHost.Contains("imap"))
                            {
                                await mediator.Send(new ReceiveMailCommand(receiveConfig, repeatLimit));
                            }
                            else
                            {
                                await mediator.Send(new ReceiveMailByPopCommand(receiveConfig, repeatLimit));
                            }
                            Thread.Sleep(200);
                        }
                        catch (Exception ex)
                        {
                            logger.LogError(ex, $"{host.Account}邮箱异常");
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "邮件接收异常");
                throw;
            }
            return Ok("启动成功!");
        }


        /// <summary>
        ///  邮件重收(测试用)
        /// </summary>
        /// <returns></returns>
        [HttpGet("ReReceiveMail")]
        [RepeatedActionFilter]
        public async Task<IActionResult> ReReceiveMail(string HostId, bool IsPrivate = false, bool IsEnabled = true)
        {
            var receiveMaillst = await mediator.Send(new MailHostQuery(IsPrivate, HostId, IsEnabled));

            var host = receiveMaillst.FirstOrDefault();
            try
            {
                var receiveConfig = new Applications.Models.MailConfigDto
                {
                    HostId = host.HostId,
                    ImapHost = host.ImapHost,
                    ImapPort = host.ImapPort,
                    SenderAccount = host.Account,
                    SenderPassword = DESHelper.DESDecrypt(host.Password),
                    ShowName = host.ShowName,
                };
                if (host.ImapHost.Contains("imap"))
                {
                    await mediator.Send(new ReceiveMailCommand(receiveConfig, 2000));
                }
                else
                {
                    await mediator.Send(new ReceiveMailByPopCommand(receiveConfig, 2000));
                }
                Thread.Sleep(200);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"{host.Account}邮箱异常");
            }
            return Ok("启动成功!");
        }

        /// <summary>
        /// 发送邮件
        /// </summary>
        /// <param name="fromEmail">发件人邮箱</param>
        /// <param name="toEmails">收件人邮箱列表</param>
        /// <param name="subject">邮件主题</param>
        /// <param name="body">邮件正文</param>
        /// <param name="ccEmails">抄送人邮箱列表（可选）</param>
        /// <param name="bccEmails">密送人邮箱列表（可选）</param>
        /// <param name="isHtml">是否为HTML邮件</param>
        /// <param name="attachmentPaths">附件路径列表（可选）</param>
        /// <param name="hostId">邮件主机ID（可选）</param>
        /// <returns>发送结果</returns>
        [HttpPost("SendMail")]
        public async Task<IActionResult> SendMail(
            [FromQuery] string fromEmail,
            [FromQuery] List<string> toEmails,
            [FromQuery] string subject,
            [FromQuery] string body,
            [FromQuery] List<string>? ccEmails = null,
            [FromQuery] List<string>? bccEmails = null,
            [FromQuery] bool isHtml = false,
            [FromQuery] List<string>? attachmentPaths = null,
            [FromQuery] string? hostId = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                // 如果没有指定hostId，尝试获取默认邮件主机
                Model.MailCenter.MailHost? host = null;
                if (string.IsNullOrEmpty(hostId))
                {
                    var hosts = await mediator.Send(new MailHostQuery(false), cancellationToken);
                    host = hosts.FirstOrDefault();
                }
                else
                {
                    var hosts = await mediator.Send(new MailHostQuery(false, hostId), cancellationToken);
                    host = hosts.FirstOrDefault();
                }

                if (host == null)
                {
                    return BadRequest("未找到可用的邮件主机");
                }

                // 准备邮件配置
                var receiveConfig = new Applications.Models.MailConfigDto
                {
                    HostId = host.HostId,
                    ImapHost = host.ImapHost,
                    ImapPort = host.ImapPort,
                    SenderAccount = host.Account,
                    SenderPassword = DESHelper.DESDecrypt(host.Password),
                    ShowName = host.ShowName
                };

                // 发送邮件命令
                var sendCommand = new SendMailBySmtpCommand(

                    ToEmails: toEmails,
                    CcEmails: ccEmails,
                    BccEmails: bccEmails,
                    Subject: subject,
                    Body: body,
                    IsHtml: isHtml,
                    AttachmentPaths: attachmentPaths,
                    ConfigDto: receiveConfig
                );

                // 执行发送
                var result = await mediator.Send(sendCommand, cancellationToken);

                return result
                    ? Ok("邮件发送成功")
                    : BadRequest("邮件发送失败");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "邮件发送异常");
                return StatusCode(500, $"邮件发送异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 发送邮件（POST方式，支持更复杂的邮件发送场景）
        /// </summary>
        [HttpPost("SendMailByBody")]
        public async Task<IActionResult> SendMailByBody(
            [FromBody] SendMailRequest request,
            CancellationToken cancellationToken = default)
        {
            try
            {
                // 验证输入
                if (request == null || request.ToEmails == null || !request.ToEmails.Any())
                {
                    return BadRequest("收件人不能为空");
                }

                // 如果没有指定hostId，尝试获取默认邮件主机
                Model.MailCenter.MailHost? host = null;
                if (string.IsNullOrEmpty(request.HostId))
                {
                    var hosts = await mediator.Send(new MailHostQuery(false), cancellationToken);
                    host = hosts.FirstOrDefault();
                }
                else
                {
                    var hosts = await mediator.Send(new MailHostQuery(false, request.HostId), cancellationToken);
                    host = hosts.FirstOrDefault();
                }

                if (host == null)
                {
                    return BadRequest("未找到可用的邮件主机");
                }

                // 准备邮件配置
                var receiveConfig = new Applications.Models.MailConfigDto
                {
                    HostId = host.HostId,
                    ImapHost = host.ImapHost,
                    ImapPort = host.ImapPort,
                    SenderAccount = host.Account,
                    SenderPassword = DESHelper.DESDecrypt(host.Password),
                    ShowName = host.ShowName,
                    SmtpHost = host.SmtpHost,
                    SmtpPort = host.SmtpPort
                };

                // 发送邮件命令
                var sendCommand = new SendMailBySmtpCommand(
                    ToEmails: request.ToEmails,
                    CcEmails: request.CcEmails,
                    BccEmails: request.BccEmails,
                    Subject: request.Subject,
                    Body: request.Body,
                    IsHtml: request.IsHtml,
                    AttachmentPaths: request.AttachmentPaths,
                    ConfigDto: receiveConfig
                );

                // 执行发送
                var result = await mediator.Send(sendCommand, cancellationToken);

                return result
                    ? Ok("邮件发送成功")
                    : BadRequest("邮件发送失败");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "邮件发送异常");
                return StatusCode(500, $"邮件发送异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 邮件发送请求模型
        /// </summary>
        public class SendMailRequest
        {
            /// <summary>
            /// 发件人邮箱（可选，默认使用系统配置）
            /// </summary>
            public string? FromEmail { get; set; }

            /// <summary>
            /// 收件人邮箱列表
            /// </summary>
            public List<string> ToEmails { get; set; } = new List<string>();

            /// <summary>
            /// 抄送人邮箱列表（可选）
            /// </summary>
            public List<string>? CcEmails { get; set; }

            /// <summary>
            /// 密送人邮箱列表（可选）
            /// </summary>
            public List<string>? BccEmails { get; set; }

            /// <summary>
            /// 邮件主题
            /// </summary>
            public string Subject { get; set; } = string.Empty;

            /// <summary>
            /// 邮件正文
            /// </summary>
            public string Body { get; set; } = string.Empty;

            /// <summary>
            /// 是否为HTML邮件
            /// </summary>
            public bool IsHtml { get; set; } = false;

            /// <summary>
            /// 附件路径列表（可选）
            /// </summary>
            public List<string>? AttachmentPaths { get; set; }

            /// <summary>
            /// 邮件主机ID（可选）
            /// </summary>
            public string? HostId { get; set; }
        }
    }
}
