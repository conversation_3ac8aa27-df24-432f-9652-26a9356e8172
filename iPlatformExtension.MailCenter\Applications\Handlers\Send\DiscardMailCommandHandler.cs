﻿﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.Send;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.Enum;
using MediatR;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Send;

/// <summary>
/// 作废邮件处理器
/// </summary>
internal sealed class DiscardMailCommandHandler(
    IMailSendRepository mailSendRepository,
    IMailSendListRepository mailSendListRepository,
    IMailReaderListRepository mailReaderListRepository,
    IFlowRecordRepository flowRecordRepository,
    IMailCorrelativeRepository mailCorrelativeRepository,
    IHttpContextAccessor httpContextAccessor) : IRequestHandler<DiscardMailCommand>
{
    public async Task Handle(DiscardMailCommand request, CancellationToken cancellationToken)
    {
        if (request.MailIds == null || !request.MailIds.Any())
        {
            throw new ApplicationException("请提供要作废的邮件ID");
        }

        var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
        var currentTime = DateTime.Now;

        // 批量处理每个邮件ID
        foreach (var mailId in request.MailIds)
        {
            // 获取邮件信息
            var mailSend = await mailSendRepository
                .Where(it => it.MailId == mailId)
                .FirstAsync(cancellationToken)
                ?? throw new ApplicationException($"邮件ID {mailId} 不存在");

            // 获取邮件发送列表信息
            var mailSendList = await mailSendListRepository
                .Where(it => it.MailId == mailId)
                .FirstAsync(cancellationToken);

            // 检查邮件是否已发送超过2分钟
            if (mailSendList != null && mailSendList.SendTime.HasValue && (currentTime - mailSendList.SendTime.Value).TotalMinutes > 2)
            {
                throw new ApplicationException($"邮件ID {mailId} 已发送超过2分钟，无法作废");
            }

            // 检查当前流程节点是否为结束节点
            var currentFlowRecord = await flowRecordRepository
                .Where(it => it.MailId == mailId && it.IsCurrent == MailFlowActionStatus.Enable.GetHashCode())
                .OrderByDescending(it => it.AuditTime)
                .FirstAsync(cancellationToken);

            if (currentFlowRecord != null && currentFlowRecord.CurNodeId != ((int)MailFlowAction.End).ToString())
            {
                throw new ApplicationException($"邮件ID {mailId} 当前流程未结束，无法作废");
            }

            // 更新邮件状态为已作废
            mailSend.Status = SendStatusType.Discarded.GetHashCode();
            await mailSendRepository.UpdateAsync(mailSend, cancellationToken);

            // 更新邮件发送列表信息
            if (mailSendList != null)
            {
                await mailSendListRepository.DeleteAsync(mailSendList, cancellationToken);
            }

            // 删除阅读人
            var readerList = await mailReaderListRepository
                .Where(it => it.MailId == mailId)
                .ToListAsync(cancellationToken);

            if (readerList.Count > 0)
            {
                await mailReaderListRepository.DeleteAsync(readerList, cancellationToken);
            }

            // 获取并删除所有关联数据
            var correlativeList = await mailCorrelativeRepository
                .Where(it => it.MailId == mailId)
                .ToListAsync(cancellationToken);

            if (correlativeList.Count > 0)
            {
                await mailCorrelativeRepository.DeleteAsync(correlativeList, cancellationToken);
            }
        }
    }

}
