﻿using System.ComponentModel;
using iPlatformExtension.Model.Attributes;

namespace iPlatformExtension.Model.Enum
{
    /// <summary>
    /// 枚举
    /// </summary>
    public static class SysEnum
    {
        /// <summary>
        /// 角色类型
        /// </summary>
        public enum RoleType
        {
            /// <summary>
            /// 管理员
            /// </summary>
            Admin = 0,

            /// <summary>
            /// 普通角色（数据库理解）
            /// </summary>
            Normal = 1,

            /// <summary>
            /// 代理作业人中心团队角色
            /// </summary>
            TrademarkOperation = 2,
        }

        /// <summary>
        /// 代理作业平台角色Code
        /// </summary>
        public enum RoleCode
        {
            /// <summary>
            /// 团队负责人
            /// </summary>
            [Description("TeamLeader")]
            TeamLeader = 0,

            /// <summary>
            /// 代理人
            /// </summary>
            [Description("Agent")]
            Agent = 1,

            /// <summary>
            /// 导师
            /// </summary>
            [Description("Mentor")]
            Mentor = 2,

            /// <summary>
            /// 立案流程
            /// </summary>
            [Description("FProcess")]
            FProcess = 3,

            /// <summary>
            /// 递交流程
            /// </summary>
            [Description("SProcess")]
            SProcess = 4,

            /// <summary>
            /// 专属流程
            /// </summary>
            [Description("EProcess")]
            EProcess = 5,
        }

        /// <summary>
        /// 团队状态
        /// </summary>
        public enum SystemTeamStatus
        {
            /// <summary>
            /// 正常
            /// </summary>
            Normal = 1,

            /// <summary>
            /// 删除
            /// </summary>
            Delete = 2,
        }

        /// <summary>
        /// 流程状态
        /// </summary>
        public enum FlowStatus
        {
            /// <summary>
            /// 取消
            /// </summary>
            [Description("取消")]
            Cancel = -1,

            /// <summary>
            /// 流程开始
            /// </summary>
            [Description("流程开始")]
            Start = 0,

            /// <summary>
            /// 流程执行
            /// </summary>
            [Description("流程执行")]
            Execute = 1000,

            /// <summary>
            /// 错误
            /// </summary>
            [Description("错误")]
            Error = 500,

            /// <summary>
            /// 完成
            /// </summary>
            [Description("完成")]
            End = 5000,
        }

        /// <summary>
        /// 报告类型
        /// </summary>
        public enum ReportType
        {
            /// <summary>
            /// 初稿期限(内)
            /// </summary>
            [Description("初稿期限(内)")]
            IntFirstDate = 1,

            /// <summary>
            /// 初稿期限(外)
            /// </summary>
            [Description("初稿期限(外)")]
            CusFirstDate = 2,

            /// <summary>
            /// 定稿期限(内)
            /// </summary>
            [Description("定稿期限(内)")]
            IntFinishDate = 3,

            /// <summary>
            /// 定稿期限(外)
            /// </summary>
            [Description("定稿期限(外)")]
            CusFinishDate = 4,

            /// <summary>
            /// 官方期限
            /// </summary>
            [Description("官方期限")]
            LegalDueDate = 5,
        }

        /// <summary>
        /// 部门���色代码
        /// </summary>
        public enum DeptRoleCode
        {
            /// <summary>
            /// 部门成员
            /// </summary>
            [Description("部门成员")]
            MB = 1,

            /// <summary>
            /// 部门负责人
            /// </summary>
            [Description("部门负责人")]
            DR = 2,
        }

        /// <summary>
        /// 公认证标识
        /// </summary>
        public enum CountryRecognized
        {
            /// <summary>
            /// 公认证国家
            /// </summary>
            RecognizedCountry = 1,

            /// <summary>
            /// 公证国家
            /// </summary>
            NotaryCountry = 2,

            /// <summary>
            /// 无
            /// </summary>
            None = 3,
        }

        /// <summary>
        /// 公认证标识
        /// </summary>
        public enum Status
        {
            /// <summary>
            /// 历史
            /// </summary>
            History = 99,

            /// <summary>
            /// 生效
            /// </summary>
            Enable = 1,

            /// <summary>
            /// 失效
            /// </summary>
            DisEnable = 2,
        }

        /// <summary>
        /// 过滤条件（匹配字段）
        /// </summary>
        [Flags]
        public enum FilterHead
        {
            /// <summary>
            /// 发件人地址
            /// </summary>
            [Description("发件人地址"), TableName("MailFroms"), FieldName("MailAddress")]
            MailFrom = 1,

            /// <summary>
            /// 收件主题
            /// </summary>
            [Description("收件主题")]
            MailSubject = 2,

            /// <summary>
            /// 抄送地址(其中一个)
            /// </summary>
            [Description("抄送地址(其中一个)"), TableName("MailUsers")]
            MailAddress = 4,

            /// <summary>
            /// 收件内容(正文+签名+回复/转发的内容)
            /// </summary>
            [Description("收件内容(正文+签名+回复/转发的内容)")]
            MailHtmlBody = 8,

            /// <summary>
            /// 附件名称
            /// </summary>
            [Description("附件名称"), TableName("MailAttachments")]
            RealName = 16,
        }

        /// <summary>
        /// 匹配方式
        /// </summary>
        [Flags]
        public enum FilterType
        {
            /// <summary>
            /// 包含
            /// </summary>
            [Description("包含")]
            Contain = 1,

            /// <summary>
            /// 相等
            /// </summary>
            [Description("等于")]
            Equal = 2,
        }

        /// <summary>
        /// 收件状态-1:取消,0:待解析,1:解析中,2:待分拣;3:办理中;4:已忽略;5:已办理;500:出错
        /// </summary>
        public enum ReceiveFileType
        {
            /// <summary>
            /// 待解析
            /// </summary>
            [Description("待解析"), IgnoreEnum()]
            WaitAnalysis = 0,

            ///// <summary>
            ///// 解析中
            ///// </summary>
            //[Description("解析中")]
            //Analysis = 1,

            /// <summary>
            /// 待分拣
            /// </summary>
            [Description("待分拣")]
            Sort = 2,

            /// <summary>
            /// 办理中
            /// </summary>
            [Description("办理中")]
            Handle = 3,

            /// <summary>
            /// 已忽略
            /// </summary>
            [Description("已忽略")]
            Ignore = 4,

            /// <summary>
            /// 已办理
            /// </summary>
            [Description("已办理")]
            Processed = 5,
        }

        /// <summary>
        /// 分拣类型,Finish:分拣到承办人,Allot:移入待分拣,Ignore:忽略
        /// </summary>
        public enum ConfigType
        {
            /// <summary>
            /// 分拣到承办人
            /// </summary>
            [Description("分拣到承办人")]
            Finish = 1,

            /// <summary>
            /// 移入待分拣
            /// </summary>
            [Description("移入待分拣")]
            Allot = 2,

            /// <summary>
            /// 忽略
            /// </summary>
            [Description("忽略")]
            Ignore = 3,
        }

        /// <summary>
        /// 在途流程状态
        /// </summary>
        public enum MailFlowActionStatus
        {
            /// <summary>
            /// 废弃历史
            /// </summary>
            History = 99,

            /// <summary>
            /// 生效
            /// </summary>
            Enable = 1,

            /// <summary>
            /// 历史流程
            /// </summary>
            DisEnable = 2,
        }

        /// <summary>
        /// 邮件动作
        /// </summary>
        public enum MailAction
        {
            /// <summary>
            /// 提交
            /// </summary>
            [Description("提交")]
            Submit = 1,

            /// <summary>
            /// 移交
            /// </summary>
            [Description("移交")]
            Transfer = 2,

            /// <summary>
            /// 退回
            /// </summary>
            [Description("退回")]
            Reject = 3,
        }

        /// <summary>
        /// 邮件流程节点
        /// </summary>
        [Flags]
        public enum MailFlowAction
        {
            /// <summary>
            /// 收件分拣
            /// </summary>
            [Description("收件分拣")]
            Allot = 1,

            /// <summary>
            /// 收件办理
            /// </summary>
            [Description("收件办理")]
            Handle = 10,

            /// <summary>
            /// 结束
            /// </summary>
            [Description("结束")]
            End = 100,
        }


        /// <summary>
        /// 发件审核节点
        /// </summary>
        [Flags]
        public enum MailFlowAudit
        {
            /// <summary>
            /// 发件办理
            /// </summary>
            [Description("发件办理")]
            SendHandle = 1,

            /// <summary>
            /// 发件审核
            /// </summary>
            [Description("发件审核")]
            Audit = 10,

            /// <summary>
            /// 结束
            /// </summary>
            [Description("结束")]
            End = 100,
        }

        /// <summary>
        /// 关联类型
        /// </summary>
        public enum CorrelateType
        {
            /// <summary>
            /// 邮件
            /// </summary>
            [Description("发件")]
            SendMail = 1,

            /// <summary>
            /// 收件
            /// </summary>
            [Description("收件")]
            ReceiveMail = 2,

            /// <summary>
            /// 案件
            /// </summary>
            [Description("案件")]
            Case = 3,

            /// <summary>
            /// 任务
            /// </summary>
            [Description("任务")]
            Proc = 4,

            /// <summary>
            /// 客户
            /// </summary>
            [Description("客户")]
            Customer = 5,
        }

        /// <summary>
        /// 解析范围
        /// </summary>
        public enum ParseScope
        {
            /// <summary>
            /// 收件主题
            /// </summary>
            [Description("收件主题")]
            MailSubject = 1,

            /// <summary>
            /// 收件内容
            /// </summary>
            [Description("收件内容")]
            MailHtmlBody = 2,

            /// <summary>
            /// 附件名称
            /// </summary>
            [Description("附件名称"), TableName("MailAttachments")]
            RealName = 3,
        }

        /// <summary>
        /// 办理类型
        /// </summary>
        public enum HandleType
        {
            /// <summary>
            /// 全部
            /// </summary>
            [Description("全部")]
            All = 0,

            /// <summary>
            /// 我分拣的
            /// </summary>
            [Description("我分拣的")]
            Allot = 1,

            /// <summary>
            /// 我承办的
            /// </summary>
            [Description("我承办的")]
            Submit = 2,

            /// <summary>
            /// 其他
            /// </summary>
            [Description("其他")]
            Other = 3,
        }

        /// <summary>
        /// 邮件类型
        /// </summary>
        public enum MailType
        {
            /// <summary>
            /// 发件
            /// </summary>
            [Description("发件")]
            Send = 1,

            /// <summary>
            /// 收件
            /// </summary>
            [Description("收件")]
            Receive = 2
        }

        /// <summary>
        /// 发送状态（发件状态）
        /// -1:取消,0:草稿,1:审核中,2:定时发送,3:待发送,4:发送失败,5:已经发送,6:已作废;500:出错
        /// </summary>
        public enum SendStatusType
        {
            /// <summary>
            /// 取消
            /// </summary>
            [Description("取消")]
            Cancel = -1,

            /// <summary>
            /// 草稿
            /// </summary>
            [Description("草稿")]
            Draft = 0,

            /// <summary>
            /// 审核中
            /// </summary>
            [Description("审核中")]
            Reviewing = 1,

            /// <summary>
            /// 定时发送
            /// </summary>
            [Description("定时发送")]
            ScheduledSend = 2,

            /// <summary>
            /// 待发送
            /// </summary>
            [Description("待发送")]
            PendingSend = 3,

            /// <summary>
            /// 发送失败
            /// </summary>
            [Description("发送失败")]
            SendFailed = 4,

            /// <summary>
            /// 已经发送
            /// </summary>
            [Description("已经发送")]
            Sent = 5,

            /// <summary>
            /// 已作废
            /// </summary>
            [Description("已作废")]
            Discarded = 6,

            /// <summary>
            /// 出错
            /// </summary>
            [Description("出错")]
            Error = 500,

            /// <summary>
            /// 进入列队
            /// </summary>
            [Description("进入列队")]
            Sending = 11,

        }
    }
}
