﻿using FreeSql.DataAnnotations;

namespace iPlatformExtension.Model.MailCenter {

	[Table(Name = "mail_send", DisableSyncStructure = true)]
	public partial class MailSend {

		[Column(Name = "mail_id", StringLength = 50, IsPrimary = true, IsNullable = false)]
		public string MailId { get; set; }

		/// <summary>
		/// 附件数量
		/// </summary>
		[Column(Name = "attachments", DbType = "int")]
		public int? Attachments { get; set; }

		/// <summary>
		/// 创建人
		/// </summary>
		[Column(Name = "create_by", StringLength = 50)]
		public string CreateBy { get; set; }

		/// <summary>
		/// 创建时间
		/// </summary>
		[Column(Name = "create_time", DbType = "datetime")]
		public DateTime? CreateTime { get; set; }

		/// <summary>
		/// 邮箱配置用户id
		/// </summary>
		[Column(Name = "host_id", StringLength = 50)]
		public string HostId { get; set; }

		/// <summary>
		/// 紧急
		/// </summary>
		[Column(Name = "is_important")]
		public bool? IsImportant { get; set; }

		/// <summary>
		/// 要求已读回执
		/// </summary>
		[Column(Name = "is_read")]
		public bool? IsRead { get; set; }

		/// <summary>
		/// 是否定时发送
		/// </summary>
		[Column(Name = "is_required_process_time")]
		public bool? IsRequiredProcessTime { get; set; }

		/// <summary>
		/// 抄送
		/// </summary>
		[Column(Name = "mail_cc", StringLength = 50)]
		public string MailCc { get; set; }

		/// <summary>
		/// 发件日期
		/// </summary>
		[Column(Name = "mail_date", DbType = "datetime")]
		public DateTime? MailDate { get; set; }

		/// <summary>
		/// 原邮件eml
		/// </summary>
		[Column(Name = "mail_eml_url", StringLength = 50)]
		public string MailEmlUrl { get; set; }

		/// <summary>
		/// 发件人
		/// </summary>
		[Column(Name = "mail_from", StringLength = 50)]
		public string MailFrom { get; set; }

		/// <summary>
		/// 邮件表头信息
		/// </summary>
		[Column(Name = "mail_header")]
		public string MailHeader { get; set; }

		/// <summary>
		/// 邮件正文
		/// </summary>
		[Column(Name = "mail_html_body", StringLength = -1)]
		public string MailHtmlBody { get; set; }

		/// <summary>
		/// 邮件名称
		/// </summary>
		[Column(Name = "mail_mark_name", StringLength = 200)]
		public string MailMarkName { get; set; }

		/// <summary>
		/// 发件编号
		/// </summary>
		[Column(Name = "mail_no", StringLength = 50)]
		public string MailNo { get; set; }

		/// <summary>
		/// 转发正文
		/// </summary>
		[Column(Name = "mail_relay_body", StringLength = -1)]
		public string MailRelayBody { get; set; }

		/// <summary>
		/// 主题
		/// </summary>
		[Column(Name = "mail_subject", StringLength = 100)]
		public string MailSubject { get; set; }

		/// <summary>
		/// 签名
		/// </summary>
		[Column(Name = "mail_text_body", StringLength = -1)]
		public string MailTextBody { get; set; }

		/// <summary>
		/// 收件人
		/// </summary>
		[Column(Name = "mail_to", StringLength = 50)]
		public string MailTo { get; set; }

		/// <summary>
		/// 签名正文
		/// </summary>
		[Column(Name = "signature_body", StringLength = -1)]
		public string SignatureBody { get; set; }

		[Column(Name = "signature_id", StringLength = 50)]
		public string SignatureId { get; set; }

		/// <summary>
		/// -1:取消,0:草稿,1:审核中,2:定时发送,3:待发送,4:发送失败,5:已经发送,6:已作废;500:出错
		/// </summary>
		[Column(Name = "status", DbType = "int")]
		public int? Status { get; set; }

		[Column(Name = "templet_id", StringLength = 50)]
		public string TempletId { get; set; }

	}

}
