﻿using iPlatformExtension.MailService.Applications.Commands;
using MailKit.Net.Imap;
using MailKit.Search;
using MailKit.Security;
using MailKit;
using MediatR;
using System.Diagnostics;
using MimeKit.IO;
using MimeKit;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Model.MailCenter;
using Microsoft.AspNetCore.Mvc.Rendering;
using iPlatformExtension.MailService.HostedService;
using iPlatformExtension.MailService.Infrastructure;
using System.Collections.Generic;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.MailService.Extensions;
using iPlatformExtension.Common.Cache;
using Microsoft.Extensions.Caching.StackExchangeRedis;
using System.Runtime.Intrinsics.X86;
using iPlatformExtension.MailService.Applications.Models;
using MimeKit.Text;
using Confluent.Kafka;
using iPlatformExtension.Common.Clients.WxWork;

namespace iPlatformExtension.MailService.Applications.Handlers
{
    /// <summary>
    /// IMAP收件
    /// </summary>
    /// <param name="freeSql"></param>
    /// <param name="logger"></param>
    public class MailReceiveHandler(IFreeSql<MailCenterFreeSql> freeSql,
        ILogger<MailReceiveHandler> logger,
        MailTool mailTool,
        IMailAttachmentsRepository mailAttachmentsRepository,
        IMailReceiveRepository mailReceiveRepository,
        IWxWorkClient wxWorkClientExtension,
         DefaultRedisCache redisCache) : IRequestHandler<ReceiveMailCommand>
    {



        public async Task Handle(ReceiveMailCommand request, CancellationToken cancellationToken)
        {
            if (request.ConfigDto == null)
            {
                throw new ArgumentNullException(nameof(ReceiveMailCommand.ConfigDto), "The parameter cannot be null.");
            }

            try
            {
                using (var client = new ImapClient(/*new ProtocolLogger(CreateMailLog())*/))
                {
                    client.Timeout = 300000;
                    await client.ConnectAsync(request.ConfigDto.ImapHost, request.ConfigDto.ImapPort.Value,
                         SecureSocketOptions.StartTls, cancellationToken);
                    await client.AuthenticateAsync(request.ConfigDto.SenderAccount, request.ConfigDto.SenderPassword, cancellationToken);
                    await client.Inbox.OpenAsync(FolderAccess.ReadOnly, cancellationToken);
                    Console.WriteLine($"启动{request.ConfigDto.SenderAccount}");
                    var inbox = client.Inbox;
                    var uids = await inbox.SearchAsync(SearchQuery.DeliveredAfter(DateTime.Parse(DateTime.Now.AddDays(-2).ToString("yyyy-MM-dd"))), cancellationToken);
                    string id = "";
                    int repeatLimit = request.repeatLimit;
                    for (var i = 1; i <= uids.Count; i++)
                    {
                        try
                        {
                            Stopwatch stopwatch = new Stopwatch();
                            stopwatch.Start();
                            var uid = uids[uids.Count - i];

                            var res = await freeSql.Select<MailReceive>().WithLock().Where(o => o.Uid == uid.Id.ToString() && o.HostId == request.ConfigDto.HostId).FirstAsync(o => o.MailId, cancellationToken);
                            if (res != null)
                            {
                                Console.WriteLine($"{uid.Id}已入库,跳过,线程数量:{mailTool.TryGetRecord().Count()}---{Thread.CurrentThread.ManagedThreadId},{request.ConfigDto.SenderAccount}总量:{i}/{uids.Count}");
                                repeatLimit--;
                                if (repeatLimit > 0)
                                {
                                    continue;
                                }
                                Console.WriteLine($"连续多次重复,停止获取,线程数量:{mailTool.TryGetRecord().Count()}---{Thread.CurrentThread.ManagedThreadId},{request.ConfigDto.SenderAccount}总量:{i}/{uids.Count}");
                                break;
                            }
                            repeatLimit = request.repeatLimit;

                            id = uid.ToString();
                            var message = await client.Inbox.GetMessageAsync(uid, cancellationToken);



                            var size = await mailTool.CalculateEmailSize(message, cancellationToken);
                            //var html = await mailTool.DownloadCid(message, request.ConfigDto.SenderAccount, cancellationToken);
                            var cids = await mailTool.DownloadCid(message, uid.ToString(), cancellationToken);
                            var mailId = Guid.NewGuid().ToString();
                            var mailAttachments = await mailTool.DownloadAttachFile(message, mailId, cancellationToken);
                            var mailUsers = mailTool.GetMailUsers(message, mailId);
                            var emlUrl = await mailTool.DownloadMailEmlAsync(message, mailId);
                            var fromUser = mailUsers.Where(o => o.AddressType == AddressTypeEnum.From).Select(o => $"{o.DisplayName}<{o.MailAddress}>").First();
                            var ccUsers = mailUsers.Where(o => o.AddressType == AddressTypeEnum.Cc).Select(o => $"{o.DisplayName}<{o.MailAddress}>").ToList();


                            var htmlBody = message.HtmlBody;
                            if (message.HtmlBody is null)
                            {
                                if (message.Body is MimeKit.Multipart multipart)
                                {
                                    foreach (var part in multipart)
                                    {
                                        if (part is MimeKit.TextPart textPart && textPart.Format == TextFormat.Html)
                                        {
                                            htmlBody = textPart.Text;
                                            break;
                                        }
                                    }
                                }
                            }

                            var mailReceive = new MailReceive
                            {
                                MailId = mailId,
                                Uid = uid.Id.ToString(),
                                HostId = request.ConfigDto.HostId,
                                MailHtmlBody = mailTool.ReplaceCid(htmlBody, cids),
                                MailCc = string.Join(",", ccUsers),
                                MailDate = message.Date.LocalDateTime,
                                MailFrom = fromUser,
                                MailTextBody = mailTool.ReplaceCid(message.TextBody, cids),
                                MailSubject = message.Subject,
                                MailTo = $"{request.ConfigDto.ShowName}<{request.ConfigDto.SenderAccount}>",
                                MailSize = size,
                                CreateTime = DateTime.Now,
                                CreateBy = "system",
                                Attachments = message.Attachments.Count(),
                                MailNo = mailTool.GetReceiptNumber(),
                                MailPriority = message.XPriority.ToString(),
                                MailEmlUrl = emlUrl
                            };

                            await freeSql.Insert(mailReceive).ExecuteAffrowsAsync(cancellationToken);
                            if (mailAttachments.Any())
                            {
                                await freeSql.Insert(mailAttachments).ExecuteAffrowsAsync(cancellationToken);
                            }
                            if (mailUsers.Any())
                            {
                                await freeSql.Insert(mailUsers).ExecuteAffrowsAsync(cancellationToken);
                            }
                            TimeSpan elapsedTime = stopwatch.Elapsed;
                            Console.WriteLine($"线程数量:{mailTool.TryGetRecord().Count()}---{Thread.CurrentThread.ManagedThreadId},{request.ConfigDto.SenderAccount}总量:{i}/{uids.Count},LongRunningMethod 耗时: {elapsedTime.TotalSeconds} 秒");
                            await Task.Delay(TimeSpan.FromMilliseconds(100), cancellationToken);
                        }
                        catch (Exception ex)
                        {
                            logger.LogError(ex, $"{request.ConfigDto.SenderAccount}-{id}邮箱获取过程中失败");
                            continue;
                        }
                    }
                    await client.Inbox.CloseAsync(false, cancellationToken);
                    await client.DisconnectAsync(true, cancellationToken);
                    Task.CompletedTask.Wait();

                    Console.WriteLine($"{request.ConfigDto.SenderAccount}获取完毕,释放连接");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{request.ConfigDto.SenderAccount}邮箱启动失败");
                logger.LogError(ex, $"{request.ConfigDto.SenderAccount}邮箱启动失败");
                if (!request.ConfigDto.IsPrivate)
                {
                    await wxWorkClientExtension.SentRobotMessageAsync($"{request.ConfigDto.SenderAccount}邮箱启动失败，确认最近是否有修改密码，请及时处理。", "MailReceive");
                }
                Task.CompletedTask.Wait();
            }
            finally {
                mailTool.TryRemove(request.ConfigDto.SenderAccount.ToString());
            }
        }



        /// <summary>
        /// 创建邮件日志文件
        /// </summary>
        /// <returns></returns>
        public static string CreateMailLog()
        {
            var logPath = AppDomain.CurrentDomain.BaseDirectory + "\\MailLog";

            if (!Directory.Exists(logPath))
            {
                Directory.CreateDirectory(logPath);
            }
            var logt = logPath + "\\" + DateTime.Now.ToString("yyyy-MM-dd") + ".txt";
            if (!File.Exists(logt))
            {
                var fs = File.Create(logt);
                fs.Close();

            }
            return logt;
        }
    }
}
