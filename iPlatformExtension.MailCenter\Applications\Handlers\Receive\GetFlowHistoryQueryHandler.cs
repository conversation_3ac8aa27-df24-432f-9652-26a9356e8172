﻿using iPlatformExtension.Common.Cache;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Models.Receive;
using iPlatformExtension.MailCenter.Applications.Queries.Receive;
using iPlatformExtension.Model.Dto;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Repository.Interface;
using MediatR;
using System.Drawing;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Receive
{
    /// <summary>
    /// 获取流程历史处理者
    /// </summary>
    internal sealed class GetFlowHistoryQueryHandler(IFreeSql<MailCenterFreeSql> freeSql, IUserInfoRepository userInfoRepository) : IRequestHandler<GetFlowHistoryQuery, IEnumerable<GetFlowHistoryDto>>
    {
        public async Task<IEnumerable<GetFlowHistoryDto>> Handle(GetFlowHistoryQuery request, CancellationToken cancellationToken)
        {
            var getFlowHistoryDtos = await freeSql.Select<FlowRecord>().WithLock()
                .Where(it => it.MailId == request.MailId && (it.IsCurrent == SysEnum.MailFlowActionStatus.DisEnable.GetHashCode()
                                                            || it.IsCurrent == SysEnum.MailFlowActionStatus.Enable.GetHashCode()))
                .OrderBy(it => it.AuditTime == null)  // 先按照是否为空排序
                .OrderBy(it => it.AuditTime)          // 再按照时间正序排序
                .ToListAsync(it => new GetFlowHistoryDto(it.Id, it.AuditType, it.MailId, it.AuditRemark, it.AuditUser,
                    it.CurNodeId, it.IsCurrent, it.AuditTime), cancellationToken);

            return await getFlowHistoryDtos.ToAsyncEnumerable().SelectAwait(async it =>
            {
                if (it.Seq is not null)
                {
                    it.ProcessNodes = request.MailType == "Send" ?((SysEnum.MailFlowAction)Enum.Parse(typeof(SysEnum.MailFlowAction), it.Seq)).GetDescription():
                    ((SysEnum.MailFlowAudit)Enum.Parse(typeof(SysEnum.MailFlowAudit), it.Seq)).GetDescription();
                }

                if (it.UndertakeUserId is not null)
                {
                    ICacheableRepository<string, UserBaseInfo> userBaseInfoRepository = userInfoRepository;
                    it.UndertakeUser = new { CnName = (await userBaseInfoRepository.GetCacheValueAsync(it.UndertakeUserId))?.CnName ?? "", UserId = it.UndertakeUserId };
                }
                return it;
            }).ToListAsync(cancellationToken: cancellationToken);
        }
    }
}

