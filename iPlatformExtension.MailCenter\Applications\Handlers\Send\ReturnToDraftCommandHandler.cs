using System;
using System.Threading;
using System.Threading.Tasks;
using iPlatformExtension.Common.Exceptions;
using iPlatformExtension.MailCenter.Applications.Commands.Send;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.Enum;
using MediatR;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Send;

/// <summary>
/// 退回草稿命令处理器
/// </summary>
/// <param name="mailSendRepository">邮件发送仓储</param>
/// <param name="mailSendFlowRepository">邮件发送流程仓储</param>
/// <param name="mailSendListRepository">邮件发送列表仓储</param>
/// <param name="flowRecordRepository">流程记录仓储</param>
public class ReturnToDraftCommandHandler(
    IMailSendRepository mailSendRepository,
    IMailSendFlowRepository mailSendFlowRepository,
    IMailSendListRepository mailSendListRepository,
    IFlowRecordRepository flowRecordRepository) : IRequestHandler<ReturnToDraftCommand>
{
    /// <summary>
    /// 处理退回草稿命令
    /// </summary>
    /// <param name="request">请求参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    public async Task Handle(ReturnToDraftCommand request, CancellationToken cancellationToken)
    {
        // 获取邮件信息
        var mail = await mailSendRepository.Where(m => m.MailId == request.MailId).FirstAsync(cancellationToken);
        if (mail == null)
        {
            throw new ApplicationException("邮件不存在");
        }

        // 检查流程是否处于第一节点
        var currentFlowRecord = await flowRecordRepository
            .Where(f => f.MailId == request.MailId && f.IsCurrent == MailFlowActionStatus.Enable.GetHashCode())
            .FirstAsync(cancellationToken);

        if (currentFlowRecord == null)
        {
            throw new ApplicationException("邮件流程不存在");
        }

        // 只有在第一个节点（收件分拣）才允许退回草稿
        if (currentFlowRecord.CurNodeId != MailFlowAction.Allot.ToString())
        {
            throw new ApplicationException("只有在流程第一节点才能退回草稿状态");
        }

        // 获取邮件发送列表信息
        var mailSendList = await mailSendListRepository.Where(m => m.MailId == request.MailId).FirstAsync(cancellationToken);
        if (mailSendList != null)
        {
            // 检查发送时间是否在2分钟内
            if (mailSendList.SendTime.HasValue && (DateTime.Now - mailSendList.SendTime.Value).TotalMinutes > 2)
            {
                throw new ApplicationException("只能退回2分钟内发送的邮件");
            }

            await mailSendListRepository.DeleteAsync(mailSendList, cancellationToken);
        }

        // 更新邮件状态为草稿
        mail.Status = SendStatusType.Draft.GetHashCode();
        mail.IsRequiredProcessTime = false;
        await mailSendRepository.UpdateAsync(mail, cancellationToken);

        // 清除相关流程数据
        var mailSendFlows = await mailSendFlowRepository.Where(f => f.MailId == request.MailId).FirstAsync(cancellationToken);
        mailSendFlows.AuditUser = "";
        await mailSendFlowRepository.UpdateAsync(mailSendFlows, cancellationToken);

        // 清除流程记录
        var flowRecords = await flowRecordRepository.Where(f => f.MailId == request.MailId).ToListAsync(cancellationToken);
        await flowRecordRepository.DeleteAsync(flowRecords, cancellationToken);
    }
}