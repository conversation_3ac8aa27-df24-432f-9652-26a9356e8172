using iPlatformExtension.Model.Enum;
using iPlatformExtension.Public.Applications.Commands.Statistics;
using iPlatformExtension.Public.Applications.Queries.Statistics;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.Public.Controllers
{
    /// <summary>
    /// 统计控制器
    /// </summary>
    [Route("[controller]")]
    [ApiController]
    public class StatisticsController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<StatisticsController> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="mediator"></param>
        /// <param name="logger"></param>
        public StatisticsController(IMediator mediator, ILogger<StatisticsController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// 1官方期限时间异常提醒
        /// </summary>
        [HttpGet]
        public async Task DueTimeErrorWarning()
        {
            await _mediator.Send(new DueTimeErrorWarningQuery(),HttpContext.RequestAborted);
        }

        /// <summary>
        /// 2.1代理人（承办人本人）端邮件模板-（周一）
        /// </summary>
        [HttpGet("Send15DeadlineMsgWarning")]
        public IActionResult Send15DeadlineMsgWarning()
        {
            // 使用异步线程执行任务，立即返回结果
            _ = Task.Run(async () =>
            {
                try
                {
                    await _mediator.Send(new Send15DeadlineMsgQuery(), CancellationToken.None);
                }
                catch (Exception ex)
                {
                    // 记录异常，但不影响响应
                    _logger.LogError(ex, "Send15DeadlineMsgWarning 后台执行异常");
                }
            });

            return Ok(new { message = "任务已启动，正在后台执行", success = true });
        }

        /// <summary>
        /// 2.2我的绝限任务15天
        /// </summary>
        [HttpGet("MineDeadlineExpired15")]
        public async Task MineDeadlineExpired15()
        {
            await _mediator.Send(new MineDeadlineExpired15Query(), HttpContext.RequestAborted);
        }

        /// <summary>
        /// 2.3我的团队任务到期提醒（15天内，含当天）
        /// </summary>
        [HttpGet("Send15DTeamWarning")]
        public async Task Send15DTeamWarning()
        {
            await _mediator.Send(new Send15DTeamWarningQuery(), HttpContext.RequestAborted);
        }

        /// <summary>
        /// 4.内部期限超过7天汇总表
        /// </summary>
        [HttpGet("InternalDeadlineExpiredDays")]
        public async Task InternalDeadlineExpiredDays([FromQuery] InternalDeadlineExpiredDaysQuery query)
        {
            await _mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 5.1应启动而未启动递交流程或管制完成的代理师任务的排查情况
        /// </summary>
        [HttpGet("CheckDelivery")]
        public async Task CheckDelivery([FromQuery] CheckDeliveryQuery query)
        {
            await _mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        ///  2.4我的团队的绝限任务到期提醒（15天内，含当天）
        /// </summary>
        [HttpGet("MyTeamDeadlineExpired15DaysWarning")]
        public async Task MyTeamDeadlineExpired15DaysWarning([FromQuery] MyTeamDeadlineExpired15DaysWarningQuery query)
        {
            await _mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 3.1 1天微信通知
        /// </summary>
        [HttpGet("WeChatWarning")]
        public async Task WeChatWarning([FromQuery] WeChatWarningQuery query)
        {
            await _mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 3.2 1天微信通知
        /// </summary>
        [HttpGet("WeChatTeamAllDeadLineWarning")]
        public async Task WeChatTeamAllDeadLineWarning([FromQuery] WeChatTeamAllDeadLineWarningQuery query)
        {
            await _mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 3.3 1天微信通知
        /// </summary>
        [HttpGet("WeChatTeamOutDeadLineWarning")]
        public async Task WeChatTeamOutDeadLineWarning([FromQuery] WeChatTeamOutDeadLineWarningQuery query)
        {
            await _mediator.Send(query, HttpContext.RequestAborted);
        }

        /// <summary>
        /// 催稿通知
        /// </summary>
        [HttpPost("UrgentDraftCommand")]
        public async Task UrgentDraftCommand([FromBody] UrgentDraftCommand query)
        {
            await _mediator.Send(query, HttpContext.RequestAborted);
        }
    }
}
