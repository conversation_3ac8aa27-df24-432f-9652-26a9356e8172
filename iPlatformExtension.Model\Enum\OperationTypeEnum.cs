﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iPlatformExtension.Model.Enum
{
    public enum OperationTypeEnum
    {
        [Description("添加")]
        Add,
        [Description("忽略")]
        Ignore,
        [Description("删除")]
        Delete,
        [Description("分拣")]
        Sort,
        [Description("提交")]
        Submit,
        [Description("退回")]
        Reject,
        [Description("移交")]
        Transfer,
    }
}
