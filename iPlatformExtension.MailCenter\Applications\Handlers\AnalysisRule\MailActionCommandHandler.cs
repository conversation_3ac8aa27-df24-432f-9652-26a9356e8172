﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.AnalysisRule;
using iPlatformExtension.MailCenter.Applications.Queries.ContentManage;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.MailCenter.Applications.Handlers.AnalysisRule
{
    /// <summary>
    /// 邮件动作（提交、移交、退回）处理者
    /// </summary>
    internal sealed class MailActionCommandHandler(
        IMailReceiveFlowRepository mailReceiveFlowRepository,
        IMailReceiveRepository mailReceiveRepository,
        IMailSendFlowRepository mailSendFlowRepository,
        IMailSendRepository mailSendRepository,
        IHttpContextAccessor context,
        IFlowRecordRepository flowRecordRepository,
        IMediator mediator,
        IMailReaderListRepository mailReaderListRepository,
        ILogger<MailActionCommand> logger,
        IFlowPrivateListRepository flowPrivateListRepository
    ) : IRequestHandler<MailActionCommand>
    {
        
        /// <summary>
        /// 处理邮件动作请求
        /// </summary>
        public async Task Handle(MailActionCommand request, CancellationToken cancellationToken)
        {
            var userId = context.HttpContext?.User.GetUserID() ?? string.Empty;

            // 获取流程记录并验证
            var flowRecordList = await GetAndValidateFlowRecords(request, cancellationToken);

            // 根据 MailType 判断邮件是收件还是发件
            bool isSendMail = request.MailType?.ToLower() == "send";

            // 根据邮件类型查询不同的仓储
            var flowRecordInsert = new List<FlowRecord>();

            // 查询邮件数据
            var (mailReceiveList, mailSendList, mailReceives, mailSends) =
                await FetchMailData(request.MailId, isSendMail, cancellationToken);
            // 处理不同的邮件动作
            if (request.Action == MailAction.Submit.ToString())
            {
                // 处理提交操作
                ProcessSubmitAction(
                    request,
                    flowRecordList,
                    isSendMail,
                    mailSendList,
                    mailReceiveList,
                    mailReceives,
                    flowRecordInsert);
            }
            else if (request.Action == MailAction.Transfer.ToString())
            {
                // 处理移交操作
                ProcessTransferAction(
                    request,
                    flowRecordList,
                    isSendMail,
                    mailSendList,
                    mailReceiveList,
                    flowRecordInsert);
            }
            else if (request.Action == MailAction.Reject.ToString())
            {
                // 处理退回操作
                ProcessRejectAction(
                    request,
                    flowRecordList,
                    flowRecordInsert);
            }

            // 更新当前流程记录状态
            UpdateFlowRecordStatus(flowRecordList, request);

            // 根据邮件类型更新数据
            if (isSendMail)
            {
                await UpdateSendMailData(
                    mailSendList,
                    mailSends,
                    flowRecordList,
                    flowRecordInsert,
                    request.MailId,
                    userId,
                    cancellationToken);
            }
            else
            {
                await UpdateReceiveMailData(
                    mailReceiveList,
                    mailReceives,
                    flowRecordList,
                    flowRecordInsert,
                    request.MailId,
                    userId,
                    cancellationToken);
            }
            try
            {
                var readerList = await mailReaderListRepository
                    .Where(o => request.MailId.Contains(o.MailId))
                    .ToListAsync(cancellationToken);
                //添加阅读人企业微信通知
                await mediator.Send(new MailCenterMessageQuery(readerList, OperationTypeEnum.Sort), cancellationToken);

                OperationTypeEnum operationTypeEnum = Enum.Parse<OperationTypeEnum>(request.Action);

                //流程微信通知
                await mediator.Send(
                    new MailCenterFlowMessageQuery(flowRecordInsert, operationTypeEnum),
                    cancellationToken
                );
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                logger.LogError(e, "微信通知触发失败.");
            }
        }

        /// <summary>
        /// 获取并验证流程记录
        /// </summary>
        private async Task<List<FlowRecord>> GetAndValidateFlowRecords(MailActionCommand request, CancellationToken cancellationToken)
        {
            var flowRecordList = await flowRecordRepository
                .Where(it => request.MailId.Contains(it.MailId))
                .OrderByDescending(it => it.AuditTime)
                .WhereIf(
                    request.Action != MailAction.Reject.ToString(),
                    it => it.IsCurrent == MailFlowActionStatus.Enable.GetHashCode()
                )
                .ToListAsync(cancellationToken);

            // 添加流程状态校验
            var currentNodes = flowRecordList
                .Where(x => x.IsCurrent == MailFlowActionStatus.Enable.GetHashCode())
                .Select(x => x.CurNodeId)
                .Distinct()
                .ToList();

            if (currentNodes.Count > 1)
            {
                throw new ApplicationException("选中的邮件必须处于相同的流程节点");
            }

            return flowRecordList;
        }

        /// <summary>
        /// 获取邮件数据
        /// </summary>
        private async Task<(List<MailReceiveFlow> mailReceiveList, List<MailSendFlow> mailSendList,
            List<MailReceive> mailReceives, List<MailSend> mailSends)>
            FetchMailData(List<string> mailIds, bool isSendMail, CancellationToken cancellationToken)
        {
            var mailReceiveList = new List<MailReceiveFlow>();
            var mailSendList = new List<MailSendFlow>();
            var mailReceives = new List<MailReceive>();
            var mailSends = new List<MailSend>();

            if (isSendMail)
            {
                // 发件邮件处理
                mailSendList = await mailSendFlowRepository
                    .Where(it => mailIds.Contains(it.MailId))
                    .ToListAsync(cancellationToken);
                mailSends = await mailSendRepository
                    .Where(it => mailIds.Contains(it.MailId))
                    .ToListAsync(cancellationToken);
            }
            else
            {
                // 收件邮件处理
                mailReceiveList = await mailReceiveFlowRepository
                    .Where(it => mailIds.Contains(it.MailId))
                    .ToListAsync(cancellationToken);
                mailReceives = await mailReceiveRepository
                    .Where(it => mailIds.Contains(it.MailId))
                    .ToListAsync(cancellationToken);
            }

            return (mailReceiveList, mailSendList, mailReceives, mailSends);
        }

        /// <summary>
        /// 获取承办人 ID
        /// </summary>
        private static string GetUndertakeUserId(string mailId, bool isSendMail, List<MailSendFlow> mailSendList,
            List<MailReceiveFlow> mailReceiveList, string? requestUndertakeUserId)
        {
            // 如果请求中指定了承办人，直接返回
            if (!string.IsNullOrWhiteSpace(requestUndertakeUserId))
            {
                return requestUndertakeUserId;
            }

            // 根据邮件类型获取承办人
            if (isSendMail)
            {
                var mailSendFlow = mailSendList.FirstOrDefault(it => it.MailId == mailId)
                    ?? throw new ApplicationException("承办人为空");
                return mailSendFlow.UndertakeUserId;
            }
            else
            {
                var mailReceiveFlow = mailReceiveList.FirstOrDefault(it => it.MailId == mailId)
                    ?? throw new ApplicationException("承办人为空");
                return mailReceiveFlow.UndertakeUserId;
            }
        }

        /// <summary>
        /// 处理发件邮件的提交操作
        /// </summary>
        private static void ProcessSendMailSubmit(
            List<MailSendFlow> mailSendList,
            List<FlowRecord> flowRecordList,
            MailActionCommand request)
        {
            mailSendList.ForEach(it =>
            {
                var node = flowRecordList.FirstOrDefault(x => it.MailId == x.MailId);
                if (node != null && Enum.Parse<MailFlowAction>(node.CurNodeId).GetEnumIndex(1) == MailFlowAction.End.ToString())
                {
                    // 对于发件邮件，设置为待发送状态
                    it.Status = SendStatusType.PendingSend.GetHashCode();
                }
                if (!string.IsNullOrWhiteSpace(request.UndertakeUserId))
                {
                    it.UndertakeUserId = request.UndertakeUserId;
                }
            });
        }

        /// <summary>
        /// 处理收件邮件的提交操作
        /// </summary>
        private static void ProcessReceiveMailSubmit(
            List<MailReceiveFlow> mailReceiveList,
            List<FlowRecord> flowRecordList,
            MailActionCommand request)
        {
            mailReceiveList.ForEach(it =>
            {
                var node = flowRecordList.FirstOrDefault(x => it.MailId == x.MailId);
                if (node != null && Enum.Parse<MailFlowAction>(node.CurNodeId).GetEnumIndex(1) == MailFlowAction.End.ToString())
                {
                    it.FinishDate = DateTime.Now;
                }
                if (!string.IsNullOrWhiteSpace(request.UndertakeUserId))
                {
                    it.UndertakeUserId = request.UndertakeUserId;
                }
            });
        }

        /// <summary>
        /// 处理发件邮件的移交操作
        /// </summary>
        private static void ProcessSendMailTransfer(
            List<MailSendFlow> mailSendList,
            string? undertakeUserId)
        {
            if (!string.IsNullOrWhiteSpace(undertakeUserId))
            {
                foreach (var mailSend in mailSendList)
                {
                    mailSend.UndertakeUserId = undertakeUserId;
                }
            }
        }

        /// <summary>
        /// 处理收件邮件的移交操作
        /// </summary>
        private static void ProcessReceiveMailTransfer(
            List<MailReceiveFlow> mailReceiveList,
            string? undertakeUserId)
        {
            if (!string.IsNullOrWhiteSpace(undertakeUserId))
            {
                foreach (var mailReceive in mailReceiveList)
                {
                    mailReceive.UndertakeUserId = undertakeUserId;
                }
            }
        }

        /// <summary>
        /// 更新发件邮件的数据
        /// </summary>
        private async Task UpdateSendMailData(
            List<MailSendFlow> mailSendList,
            List<MailSend> mailSends,
            List<FlowRecord> flowRecordList,
            List<FlowRecord> flowRecordInsert,
            List<string> mailIds,
            string userId,
            CancellationToken cancellationToken)
        {
            // 更新发件邮件流程信息
            mailSendList.ForEach(it =>
            {
                it.UpdateTime = DateTime.Now;
                it.UpdateBy = userId;
            });

            var flowPrivateList = await flowPrivateListRepository
                .Where(it => mailIds.Contains(it.MailId))
                .ToListAsync(cancellationToken);
            await flowPrivateListRepository.DeleteAsync(flowPrivateList, cancellationToken);
            await mailSendRepository.UpdateAsync(mailSends, cancellationToken);
            await flowRecordRepository.UpdateAsync(flowRecordList, cancellationToken);
            await flowRecordRepository.InsertAsync(flowRecordInsert, cancellationToken);
            await mailSendFlowRepository.UpdateAsync(mailSendList, cancellationToken);
        }

        /// <summary>
        /// 更新收件邮件的数据
        /// </summary>
        private async Task UpdateReceiveMailData(
            List<MailReceiveFlow> mailReceiveList,
            List<MailReceive> mailReceives,
            List<FlowRecord> flowRecordList,
            List<FlowRecord> flowRecordInsert,
            List<string> mailIds,
            string userId,
            CancellationToken cancellationToken)
        {
            // 更新收件邮件流程信息
            mailReceiveList.ForEach(it =>
            {
                it.UpdateTime = DateTime.Now;
                it.UpdateBy = userId;
            });

            var flowPrivateList = await flowPrivateListRepository
                .Where(it => mailIds.Contains(it.MailId))
                .ToListAsync(cancellationToken);
            await flowPrivateListRepository.DeleteAsync(flowPrivateList, cancellationToken);
            await mailReceiveRepository.UpdateAsync(mailReceives, cancellationToken);
            await flowRecordRepository.UpdateAsync(flowRecordList, cancellationToken);
            await flowRecordRepository.InsertAsync(flowRecordInsert, cancellationToken);
            await mailReceiveFlowRepository.UpdateAsync(mailReceiveList, cancellationToken);
        }

        /// <summary>
        /// 更新流程记录状态
        /// </summary>
        private static void UpdateFlowRecordStatus(List<FlowRecord> flowRecordList, MailActionCommand request)
        {
            foreach (var flowRecord in flowRecordList)
            {
                if (flowRecord.IsCurrent == MailFlowActionStatus.Enable.GetHashCode())
                {
                    flowRecord.IsCurrent = MailFlowActionStatus.DisEnable.GetHashCode();
                    flowRecord.AuditType = request.Action;
                    flowRecord.AuditRemark = request.DisplayName ?? "";
                    flowRecord.AuditTime = DateTime.Now;
                }
            }
        }

        /// <summary>
        /// 处理提交操作
        /// </summary>
        private static void ProcessSubmitAction(
            MailActionCommand request,
            List<FlowRecord> flowRecordList,
            bool isSendMail,
            List<MailSendFlow> mailSendList,
            List<MailReceiveFlow> mailReceiveList,
            List<MailReceive> mailReceives,
            List<FlowRecord> flowRecordInsert)
        {
            foreach (var mailId in request.MailId)
            {
                var lastNode = flowRecordList.FirstOrDefault(x => mailId == x.MailId);
                if (lastNode is null || lastNode.CurNodeId == MailFlowAction.End.ToString())
                {
                    throw new ApplicationException("流程已结束或尚未启动");
                }

                // 获取承办人 ID
                string undertakeUserId = GetUndertakeUserId(mailId, isSendMail, mailSendList, mailReceiveList, request.UndertakeUserId);

                var node = Enum.Parse<MailFlowAction>(lastNode.CurNodeId).GetEnumIndex(1);
                var flowRecord = new FlowRecord
                {
                    Id = Guid.NewGuid().ToString(),
                    AuditUser = (
                        node == MailFlowAction.End.ToString()
                            ? null
                            : undertakeUserId
                    )!,
                    CurNodeId = node ?? throw new ApplicationException("获取下一步流程错误"),
                    IsCurrent =
                        node == MailFlowAction.End.ToString()
                            ? MailFlowActionStatus.DisEnable.GetHashCode()
                            : MailFlowActionStatus.Enable.GetHashCode(),
                    MailId = mailId,
                    Version = "1",
                    PreRecordId = lastNode.Id,
                    AuditTime = null,
                };
                flowRecordInsert.Add(flowRecord);

                // 更新收件邮件状态
                mailReceives.ForEach(it =>
                {
                    if (it.MailId != mailId)
                        return;
                    if (flowRecord.CurNodeId == MailFlowAction.End.ToString())
                    {
                        it.Status = ReceiveFileType.Processed.GetHashCode();
                    }
                });
            }

            // 根据邮件类型处理不同的业务逻辑
            if (isSendMail)
            {
                ProcessSendMailSubmit(mailSendList, flowRecordList, request);
            }
            else
            {
                ProcessReceiveMailSubmit(mailReceiveList, flowRecordList, request);
            }
        }

        /// <summary>
        /// 处理移交操作
        /// </summary>
        private static void ProcessTransferAction(
            MailActionCommand request,
            List<FlowRecord> flowRecordList,
            bool isSendMail,
            List<MailSendFlow> mailSendList,
            List<MailReceiveFlow> mailReceiveList,
            List<FlowRecord> flowRecordInsert)
        {
            // 验证当前节点
            foreach (var mailId in request.MailId)
            {
                var currentNode = flowRecordList.FirstOrDefault(x =>
                    x.MailId == mailId
                    && x.IsCurrent == MailFlowActionStatus.Enable.GetHashCode()
                );
                if (
                    currentNode is null
                    || currentNode.CurNodeId == MailFlowAction.Allot.ToString()
                )
                {
                    throw new ApplicationException("当前节点是收件分拣节点不允许提交");
                }
            }

            // 根据邮件类型处理不同的业务逻辑
            if (isSendMail)
            {
                ProcessSendMailTransfer(mailSendList, request.UndertakeUserId);
            }
            else
            {
                ProcessReceiveMailTransfer(mailReceiveList, request.UndertakeUserId);
            }

            // 创建新的流程记录
            foreach (var mailId in request.MailId)
            {
                flowRecordInsert.Add(
                    new FlowRecord
                    {
                        Id = Guid.NewGuid().ToString(),
                        AuditUser = GetUndertakeUserId(mailId, isSendMail, mailSendList, mailReceiveList, request.UndertakeUserId),
                        CurNodeId = flowRecordList.First(x => mailId == x.MailId).CurNodeId,
                        PreRecordId =
                            flowRecordList
                                .OrderByDescending(x => x.AuditTime)
                                .FirstOrDefault(x => x.MailId == mailId)
                                ?.Id ?? "",
                        IsCurrent = MailFlowActionStatus.Enable.GetHashCode(),
                        MailId = mailId,
                        Version = "1",
                        AuditTime = null,
                    }
                );
            }
        }

        /// <summary>
        /// 处理退回操作
        /// </summary>
        private static void ProcessRejectAction(
            MailActionCommand request,
            List<FlowRecord> flowRecordList,
            List<FlowRecord> flowRecordInsert)
        {
            foreach (var mailId in request.MailId)
            {
                // 获取当前节点
                var currentNodeId = flowRecordList
                    .Where(it => it.IsCurrent == MailFlowActionStatus.Enable.GetHashCode())
                    .FirstOrDefault(x => mailId == x.MailId)
                    ?.CurNodeId;

                if (string.IsNullOrEmpty(currentNodeId))
                {
                    throw new ApplicationException("找不到当前流程节点");
                }

                // 获取上一个节点和处理人
                var node = Enum.Parse<MailFlowAction>(currentNodeId).GetEnumIndex(-1)?.ToString() ?? string.Empty;
                var lastUser = flowRecordList
                    .OrderByDescending(x => x.AuditTime)
                    .FirstOrDefault(x => x.MailId == mailId && x.CurNodeId == node)
                    ?.AuditUser
                    ?? throw new ApplicationException("找不到上一条流程处理人");

                // 创建新的流程记录
                var flowRecord = new FlowRecord
                {
                    Id = Guid.NewGuid().ToString(),
                    AuditUser = lastUser,
                    CurNodeId = node,
                    PreRecordId =
                        flowRecordList
                            .Where(x =>
                                x.IsCurrent == MailFlowActionStatus.Enable.GetHashCode()
                            )
                            .OrderByDescending(x => x.AuditTime)
                            .FirstOrDefault(x => x.MailId == mailId)
                            ?.Id ?? "",
                    IsCurrent = MailFlowActionStatus.Enable.GetHashCode(),
                    MailId = mailId,
                    Version = "1",
                    AuditTime = null,
                };
                flowRecordInsert.Add(flowRecord);
            }
        }
    }
}
