﻿using MimeKit.IO;
using MimeKit;
using iPlatformExtension.Common.Clients.HuaweiObs;
using System.Net;
using iPlatformExtension.Model.MailCenter;
using iPlatformExtension.Common.Clients.HuaweiObs.Options;
using Microsoft.Extensions.Options;
using iPlatformExtension.Common.Cache;
using Microsoft.Extensions.Caching.StackExchangeRedis;
using CSScriptLib;
using SharpCompress.Common;
using System.IO;
using Google.Protobuf.WellKnownTypes;
using Org.BouncyCastle.Utilities.Zlib;
using Org.BouncyCastle.Utilities;
using System.Collections;
using System.Collections.Concurrent;
using Org.BouncyCastle.Bcpg.OpenPgp;
using iPlatformExtension.MailService.Extensions;
using iPlatformExtension.MailService.Applications.Models;
using Confluent.Kafka;

namespace iPlatformExtension.MailService.Infrastructure
{
    public sealed class MailTool(HuaweiObsClient huaweiObsClient, IOptionsMonitor<ObsClientOptions> ObsOptions, DefaultRedisCache redisCache)
    {
        private static ConcurrentDictionary<string, string> ThreadRecord = new ConcurrentDictionary<string, string>();


        public void TryAdd(string key, string value)
        {
            if (ThreadRecord == null)
            {
                ThreadRecord = new ConcurrentDictionary<string, string>();
            }
            else
            {
                ThreadRecord.TryAdd(key, value);
            }
        }

        public void TryRemove(string key)
        {
            if (ThreadRecord == null)
            {
                ThreadRecord = new ConcurrentDictionary<string, string>();
            }
            else
            {
                ThreadRecord.TryRemove(key, out _);
            }
        }

        public ConcurrentDictionary<string, string> TryGetRecord()
        {
            if (ThreadRecord == null)
            {
                ThreadRecord = new ConcurrentDictionary<string, string>();
            }
            return ThreadRecord;
        }




        public async Task<int> CalculateEmailSize(MimeMessage message, CancellationToken cancellationToken = default(CancellationToken))
        {
            // 创建一个MeasuringStream用于计算流的大小
            using (var measuringStream = new MeasuringStream())
            {
                // 将邮件消息保存到测量流中
                await message.WriteToAsync(measuringStream, cancellationToken);

                // 返回测量得到的字节总数
                return (int)measuringStream.Length;
            }
        }


        public async Task<string> DownloadMailEmlAsync(MimeMessage message, string mailId)
        {
            var filePath = $"attachments/{mailId}/{mailId}.eml";
            using (var stream = new MemoryStream())
            {
                stream.Position = 0;
                await message.WriteToAsync(stream);
                byte[] emailBytes = stream.ToArray();
                var result = await huaweiObsClient.PutFileAsync(filePath, new MemoryStream(emailBytes));
                Console.WriteLine($"{filePath}文件上传结果:{result.StatusCode}");
                return result.StatusCode == HttpStatusCode.OK ? result.ObjectUrl : string.Empty;
            }
        }

        /// <summary>
        /// 正文图片处理
        /// </summary>
        /// <param name="message"></param>
        /// <param name="filepath"></param>
        public async Task<Dictionary<string, string>> DownloadCid(MimeMessage message, string uid, CancellationToken cancellationToken = default(CancellationToken))
        {
            var folderPath = $"images/{uid}";
            var html = message.HtmlBody;
            var parts = message.BodyParts.Where(o => o.ContentType.MediaType == "image" && o.ContentId != null);
            var dic = new Dictionary<string, string>();
            foreach (MimePart part in parts)
            {
                var filePath = $"{folderPath}/{part.ContentId}{Path.GetExtension(part.FileName)}";
                using (var stream = new MemoryBlockStream())
                {
                    await part.Content.DecodeToAsync(stream, cancellationToken);
                    stream.Position = 0;
                    var result = await huaweiObsClient.PutFileAsync(filePath, stream);
                    Console.WriteLine($"{filePath}文件上传成功{result.StatusCode}");
                    if (result.StatusCode == HttpStatusCode.OK)
                    {
                        dic.Add($"cid:{part.ContentId}", result.ObjectUrl);
                        // html = html.Replace($"cid:{part.ContentId}", result.ObjectUrl);
                    }
                }
            }
            return dic;
        }


        public string ReplaceCid(string html, Dictionary<string, string> dic)
        {
            if (!string.IsNullOrEmpty(html))
            {
                foreach (var (contentId, url) in dic)
                {
                    html = html.Replace($"{contentId}", url);
                }
            }
            return html;
        }


        /// <summary>
        /// 附件处理
        /// </summary>
        /// <param name="message"></param>
        /// <param name="filepath"></param>
        public async Task<List<MailAttachments>> DownloadAttachFile(MimeMessage message, string mailId, CancellationToken cancellationToken = default(CancellationToken))
        {
            List<MailAttachments> lst = new List<MailAttachments>();
            var folderPath = $"attachments/{mailId}";
            foreach (var part in message.Attachments.ToList())
            {
                if (part is MimePart mimePart)
                {
                    if (Path.GetExtension(mimePart.FileName) == ".dat")
                    {
                        continue;
                    }
                    var AttachmentId = Guid.NewGuid().ToString();
                    var fileName = $"{AttachmentId}{Path.GetExtension(mimePart.FileName)}";
                    var filePath = $"{folderPath}/{fileName}";
                    await using var stream = new MemoryBlockStream();
                    await mimePart.Content.DecodeToAsync(stream, cancellationToken);
                    var fileSize = stream.Length;
                    if (lst.Any(o => o.RealName == mimePart.FileName && o.FileSize == fileSize))
                    {
                        continue;
                    }
                    stream.Position = 0;
                    var result = await huaweiObsClient.PutFileAsync(filePath, stream);
                    if (result.StatusCode == HttpStatusCode.OK)
                    {
                        lst.Add(new MailAttachments
                        {
                            AttachmentId = AttachmentId,
                            Bucket = ObsOptions.CurrentValue.DefaultBucketName,
                            FileName = fileName,
                            FileSize = fileSize,
                            InputTime = DateTime.Now,
                            MailId = mailId,
                            RealName = mimePart.FileName,
                            ServerPath = folderPath,
                            Extension = Path.GetExtension(mimePart.FileName)

                        });
                        Console.WriteLine($"{filePath}上传成功!");
                    }
                }
                if (part is MessagePart messagePart)
                {
                    if (Path.GetExtension(messagePart.ContentDisposition?.FileName) == ".dat" )
                    {
                        continue;
                    }
                    var AttachmentId = Guid.NewGuid().ToString();
                    var fileName = $"{AttachmentId}{Path.GetExtension(messagePart.ContentDisposition?.FileName)}";
                    var filePath = $"{folderPath}/{fileName}";
                    await using var stream = new MemoryBlockStream();
                    await messagePart.Message.WriteToAsync(stream, cancellationToken);
                    var fileSize = stream.Length;
                    if (lst.Any(o => o.RealName == messagePart.ContentDisposition?.FileName && o.FileSize == fileSize))
                    {
                        continue;
                    }
                    stream.Position = 0;
                    var result = await huaweiObsClient.PutFileAsync(filePath, stream);
                    if (result.StatusCode == HttpStatusCode.OK)
                    {
                        lst.Add(new MailAttachments
                        {
                            AttachmentId = AttachmentId,
                            Bucket = ObsOptions.CurrentValue.DefaultBucketName,
                            FileName = fileName,
                            FileSize = fileSize,
                            InputTime = DateTime.Now,
                            MailId = mailId,
                            RealName = messagePart.ContentDisposition?.FileName,
                            ServerPath = folderPath,
                            Extension = Path.GetExtension(messagePart.ContentDisposition?.FileName)
                        });
                        Console.WriteLine($"{filePath}上传成功!");
                    }
                }
            }

            foreach (var part in message.BodyParts)
            {
                if (part is MimeKit.MimePart mimePart)
                {
                    if (string.IsNullOrEmpty(mimePart.FileName) || (!string.IsNullOrEmpty(mimePart.FileName) && Path.GetExtension(mimePart.FileName).ToLower() != ".pdf"))
                    {
                        continue;
                    }
                    var AttachmentId = Guid.NewGuid().ToString();
                    var fileName = $"{AttachmentId}{Path.GetExtension(mimePart.FileName)}";
                    var filePath = $"{folderPath}/{fileName}";
                    await using var stream = new MemoryBlockStream();
                    await mimePart.Content.DecodeToAsync(stream, cancellationToken);
                    var fileSize = stream.Length;
                    if (lst.Any(o => o.RealName == mimePart.FileName && o.FileSize == fileSize))
                    {
                        continue;
                    }
                    stream.Position = 0;
                    var result = await huaweiObsClient.PutFileAsync(filePath, stream);
                    if (result.StatusCode == HttpStatusCode.OK)
                    {
                        lst.Add(new MailAttachments
                        {
                            AttachmentId = AttachmentId,
                            Bucket = ObsOptions.CurrentValue.DefaultBucketName,
                            FileName = fileName,
                            FileSize = fileSize,
                            InputTime = DateTime.Now,
                            MailId = mailId,
                            RealName = mimePart.FileName,
                            ServerPath = folderPath,
                            Extension = Path.GetExtension(mimePart.FileName)

                        });
                        Console.WriteLine($"{filePath}上传成功!");
                    }
                }
                if (part is MessageDeliveryStatus messageDeliveryStatus)
                {
                    var AttachmentId = Guid.NewGuid().ToString();
                    var fileName = $"{AttachmentId}.messageDeliveryStatus";
                    var filePath = $"{folderPath}/{fileName}";
                    using (var stream = new MemoryStream())
                    {
                        stream.Position = 0;
                        await messageDeliveryStatus.WriteToAsync(stream);
                        byte[] emailBytes = stream.ToArray();
                        var fileSize = stream.Length;
                        var result = await huaweiObsClient.PutFileAsync(filePath, new MemoryStream(emailBytes));
                        Console.WriteLine($"{filePath}文件上传结果:{result.StatusCode}");
                        lst.Add(new MailAttachments
                        {
                            AttachmentId = AttachmentId,
                            Bucket = ObsOptions.CurrentValue.DefaultBucketName,
                            FileName = fileName,
                            FileSize = fileSize,
                            InputTime = DateTime.Now,
                            MailId = mailId,
                            RealName = fileName,
                            ServerPath = folderPath,
                            Extension = Path.GetExtension(messageDeliveryStatus.ContentDisposition?.FileName)
                        });
                    }
                }

                if (part is MessagePart messagePart)
                {
                    var realName = messagePart.ContentDisposition?.FileName;
                    if (string.IsNullOrEmpty(realName))
                    {
                        realName = $"{messagePart.Message.Subject}.eml";
                    }


                    var AttachmentId = Guid.NewGuid().ToString();
                    var fileName = $"{AttachmentId}.eml";
                    var filePath = $"{folderPath}/{fileName}";
                    using (var stream = new MemoryStream())
                    {
                        stream.Position = 0;
                        await messagePart.WriteToAsync(stream);
                        var fileSize = stream.Length;
                        if (lst.Any(o => o.RealName == realName && o.FileSize == fileSize))
                        {
                            continue;
                        }


                        byte[] emailBytes = stream.ToArray();
                        var result = await huaweiObsClient.PutFileAsync(filePath, new MemoryStream(emailBytes));
                        Console.WriteLine($"{filePath}文件上传结果:{result.StatusCode}");
                        lst.Add(new MailAttachments
                        {
                            AttachmentId = AttachmentId,
                            Bucket = ObsOptions.CurrentValue.DefaultBucketName,
                            FileName = fileName,
                            FileSize = fileSize,
                            InputTime = DateTime.Now,
                            MailId = mailId,
                            RealName = realName,
                            ServerPath = folderPath,
                            Extension = Path.GetExtension(messagePart.ContentDisposition?.FileName)
                        });
                    }
                }
            }
            return lst;
        }


        /// <summary>
        /// 获取收件人信息
        /// </summary>
        /// <param name="message"></param>
        /// <param name="mailId"></param>
        /// <returns></returns>
        public List<MailUser> GetMailUsers(MimeMessage message, string mailId)
        {
            List<MailUser> mailUsers = new List<MailUser>();

            var from_display_name = string.Empty;
            if (message.From.Count > 0)
            {
                var from = (MailboxAddress)(message.From?[0]);
                from_display_name = $"{(!string.IsNullOrWhiteSpace(from.Name) ? from.Name : from.LocalPart)}";

                mailUsers.Add(new MailUser()
                {
                    Id = Guid.NewGuid().ToString(),
                    AddressType = AddressTypeEnum.From,
                    DisplayName = from_display_name,
                    MailId = mailId,
                    MailAddress = from.Address
                });
            }
            message.To.ForEach(o =>
            {
                if (o is MailboxAddress mailboxAddress)
                {
                    mailUsers.Add(UserAddress(mailId, mailboxAddress, AddressTypeEnum.To));
                }
                if (o is GroupAddress groupAddress)
                {
                    groupAddress.Members.ForEach(m =>
                    {
                        mailUsers.Add(UserAddress(mailId, m, AddressTypeEnum.To));
                    });
                }
            });
            message.Cc.ForEach(o =>
            {
                if (o is MailboxAddress mailboxAddress)
                {
                    mailUsers.Add(UserAddress(mailId, mailboxAddress, AddressTypeEnum.Cc));
                }
                if (o is GroupAddress groupAddress)
                {
                    groupAddress.Members.ForEach(m =>
                    {
                        mailUsers.Add(UserAddress(mailId, m, AddressTypeEnum.Cc));
                    });
                }
            });

            return mailUsers;
        }

        private MailUser UserAddress(string mailId, InternetAddress internetAddress, string AddressType)
        {
            if (internetAddress is MailboxAddress Address)
            {
                return new MailUser()
                {
                    Id = Guid.NewGuid().ToString(),
                    AddressType = AddressType,
                    DisplayName = internetAddress.Name,
                    MailId = mailId,
                    MailAddress = Address.Address
                };
            }
            else
            {
                return new MailUser()
                {
                    Id = Guid.NewGuid().ToString(),
                    AddressType = AddressType,
                    DisplayName = internetAddress.Name,
                    MailId = mailId
                };
            }
        }

        private static readonly string lockReceiptNumber = "ReceiptNumber";

        /// <summary>
        /// 获取收件编号
        /// </summary>
        /// <returns></returns>
        public string GetReceiptNumber()
        {
            //todo:改成分布式锁
            lock (lockReceiptNumber)
            {
                var key = "ReceiptNumber";

                //收件编号：SJ+年份后两位+两位数月份+两位数日期+两位数小时+四位数序号
                var no = redisCache.CurrentDatabase.HashIncrement(key, 1);
                var dt = DateTime.Now;
                if (no < 10)
                {
                    var ts = DateTime.Now.Date.AddDays(1).AddSeconds(-10) - dt;
                    redisCache.CurrentDatabase.KeyExpire(key, ts);
                }
                return $"SJ{dt.ToString("yyMMddHH")}{no.ToString().PadLeft(4, '0')}";
            }
        }

        private static readonly string lockSendNumber = "ReceiptNumber";
        /// <summary>
        /// 获取发件编号
        /// </summary>
        /// <returns></returns>
        public async Task<string> GetSendNumber()
        {
            lock (lockSendNumber)
            {
                var key = "SendNumber";
                //收件编号：FJ+年份后两位+两位数月份+两位数日期+两位数小时+四位数序号
                //收件编号：SJ+年份后两位+两位数月份+两位数日期+两位数小时+四位数序号
                var no = redisCache.CurrentDatabase.HashIncrement(key, 1);
                var dt = DateTime.Now;
                if (no < 10)
                {
                    var ts = DateTime.Now.Date.AddDays(1).AddSeconds(-10) - dt;
                    redisCache.CurrentDatabase.KeyExpire(key, ts);
                }
                return $"FJ{dt.ToString("yyMMddHH")}{no.ToString().PadLeft(4, '0')}";
            }
        }
    }
}
