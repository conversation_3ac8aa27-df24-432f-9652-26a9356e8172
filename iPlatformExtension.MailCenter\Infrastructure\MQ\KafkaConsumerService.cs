﻿using Confluent.Kafka;
using iPlatformExtension.Common.MQ.MailMQ;
using iPlatformExtension.MailCenter.Applications.Queries.ContentManage;
using iPlatformExtension.Model.Dto.MailCenter;
using MediatR;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.MailCenter.Infrastructure.MQ
{
    public class KafkaConsumerService<TKey, TValue> : IDisposable
    {
        private readonly KafkaConsumerFactory<TKey, TValue> _consumerFactory;
        private readonly ILogger<KafkaConsumerService<TKey, TValue>> _logger;
        private readonly KafkaConsumerSettings _config;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private IConsumer<TKey, TValue> _consumer;
        private Task _consumeTask;

        public KafkaConsumerService(
            KafkaConsumerFactory<TKey, TValue> consumerFactory, 
            ILogger<KafkaConsumerService<TKey, TValue>> logger, 
            IOptions<KafkaConsumerSettings> config, 
            IServiceScopeFactory serviceScopeFactory)
        {
            _consumerFactory = consumerFactory;
            _logger = logger;
            _config = config.Value;
            _serviceScopeFactory = serviceScopeFactory;
        }

        public async Task StartConsumingAsync(CancellationToken stoppingToken)
        {
            try
            {
                _consumer = _consumerFactory.CreateConsumer();
                _consumer.Subscribe(_config.Topic);

                _logger.LogInformation($"Kafka consumer started for topic: {_config.Topic}");

                _consumeTask = Task.Run(() => ConsumeMessagesAsync(stoppingToken));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to start Kafka consumer for topic: {_config.Topic}");
                throw;
            }
        }

        private async Task ConsumeMessagesAsync(CancellationToken stoppingToken)
        {
            try
            {
                while (!stoppingToken.IsCancellationRequested)
                {
                    try
                    {
                        var consumeResult = _consumer.Consume(stoppingToken);

                        if (consumeResult.IsPartitionEOF)
                        {
                            _logger.LogDebug($"Reached end of partition for topic {_config.Topic}");
                            continue;
                        }

                        if (consumeResult.Message.Value is MailCenterMessageContent mailMessage)
                        {
                            await ProcessMessageAsync(mailMessage);
                        }
                    }
                    catch (ConsumeException ex)
                    {
                        _logger.LogError(ex, $"Consume error in topic {_config.Topic}: {ex.Error.Reason}");
                    }
                    catch (OperationCanceledException)
                    {
                        // 正常的取消操作
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Unexpected error in Kafka consumer for topic: {_config.Topic}");
            }
            finally
            {
                _consumer?.Close();
            }
        }

        private async Task ProcessMessageAsync(MailCenterMessageContent mailMessage)
        {
            try
            {
                using (var scope = _serviceScopeFactory.CreateScope())
                {
                    var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
                    await mediator.Send(new ReaderChangeMessageQuery(mailMessage));

                    _logger.LogInformation($"Successfully processed message: {mailMessage.MailNo}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error processing message {mailMessage.MailNo}");
            }
        }

        public async Task StopConsumingAsync()
        {
            _logger.LogInformation($"Stopping Kafka consumer for topic: {_config.Topic}");
           // _cancellationTokenSource.Cancel();

            if (_consumeTask != null)
            {
                await _consumeTask;
            }
        }

        public void Dispose()
        {
            //_cancellationTokenSource?.Cancel();
            _consumer?.Dispose();
           // _cancellationTokenSource?.Dispose();
        }
    }
}
