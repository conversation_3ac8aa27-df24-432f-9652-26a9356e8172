using System.Linq.Expressions;
using FreeSql.Internal.Model;
using Google.Protobuf;
using Grpc.Core;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.GrpcServices;
using iPlatformExtension.Messages.Mails;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Public.Applications.Models.Statistics;
using iPlatformExtension.Public.Applications.Queries.Statistics;
using iPlatformExtension.Repository.Interface;
using MediatR;
using MiniExcelLibs;
using MongoDB.Bson;

namespace iPlatformExtension.Public.Applications.Handlers.Statistics
{
    /// <summary>
    /// 期限判断计算
    /// </summary>
    internal sealed class DueTimeErrorWarningQueryHandler(
        IFreeSql freeSql,
        IBaseCtrlProcRepository iBaseCtrlProcRepository,
        IUserInfoRepository userInfoRepository,
        ICompanyRepository iCompanyRepository,
        Notification.NotificationClient notificationClient,
        ILogger<DueTimeErrorWarningQuery> logger,
        IHostEnvironment hostEnvironment
    ) : IRequestHandler<DueTimeErrorWarningQuery, IEnumerable<DueTimeErrorWarningDto>>
    {
        public async Task<IEnumerable<DueTimeErrorWarningDto>> Handle(
            DueTimeErrorWarningQuery request,
            CancellationToken cancellationToken
        )
        {
            var rightList = new List<string>() { "IntFirstDate" };
            //初稿期限（外）
            var cusFirstDateExpression = FreeSqlExtension.OrThanExpression<CaseProcInfo, CaseInfo>(
                "CusFirstDate",
                rightList,
                DynamicFilterOperator.LessThan
            );
            //定稿期限（内）
            rightList.Add("CusFirstDate");
            var intFinishDateExpression = FreeSqlExtension.OrThanExpression<CaseProcInfo, CaseInfo>(
                "IntFinishDate",
                rightList,
                DynamicFilterOperator.LessThan
            );
            //定稿期限（外）
            rightList.Add("IntFinishDate");
            var cusFinishDateExpression = FreeSqlExtension.OrThanExpression<CaseProcInfo, CaseInfo>(
                "CusFinishDate",
                rightList,
                DynamicFilterOperator.LessThan
            );
            //官方期限
            rightList.Add("CusFinishDate");
            var legalDueDateExpression = FreeSqlExtension.OrThanExpression<CaseProcInfo, CaseInfo>(
                "LegalDueDate",
                rightList,
                DynamicFilterOperator.LessThan
            );
            //初稿期限（内）>=初稿期限（外）>=定稿期限（内）>=定稿期限（外）>=官方期限
            //专利内内-专利内外且申请类型为PCT国际申请添加
            var select = await freeSql
                .Select<CaseProcInfo, CaseInfo>()
                .LeftJoin(it => it.t1.CaseId == it.t2.Id)
                .Where(it =>
                    (it.t2.CaseDirection == "II" && it.t2.CaseTypeId == "P")
                    || (
                        it.t2.CaseDirection == "IO"
                        && it.t2.CaseTypeId == "P"
                        && it.t2.ApplyTypeId == "8B9326B4-C566-4044-B017-CFAE68236F17"
                    )
                )
                .Where(it => it.t1.FinishDate == null)
                .Where(
                    cusFirstDateExpression
                        .Or(cusFinishDateExpression)
                        .Or(intFinishDateExpression)
                        .Or(legalDueDateExpression)
                )
                .WithLock()
                .ToListAsync(
                    it => new DueTimeErrorWarningDto(
                        it.t2.Volume,
                        it.t2.BelongCompany,
                        it.t2.ManageCompany,
                        it.t2.CaseName,
                        it.t1.CtrlProcId,
                        it.t1.IntFirstDate,
                        it.t1.CusFirstDate,
                        it.t1.IntFinishDate,
                        it.t1.CusFinishDate,
                        it.t1.LegalDueDate,
                        it.t2.CustomerId,
                        it.t1.UndertakeUserId
                    ),
                    cancellationToken
                );
            var customerList = select.Select(it => it.CustomerId).ToList().Distinct();
            var cusCustomers = await freeSql
                .Select<CusCustomer, CusJoinList>()
                .LeftJoin(it =>
                    it.t1.CustomerId == it.t2.FormObjId
                    && it.t2.JoinType == "controls_identified"
                    && it.t2.JoinObjId == "10AD5B3F-B71F-4E3F-987A-00F0F717DB1E"
                )
                .Where(it => customerList.Contains(it.t1.CustomerId))
                .ToListAsync(
                    it => new
                    {
                        it.t1.CustomerId,
                        it.t1.CustomerName,
                        IsExclusive = !string.IsNullOrWhiteSpace(it.t2.JoinId) ? "是" : "否",
                    },
                    cancellationToken
                );

            var dueTimeErrorWarningDtos = await select
                .ToAsyncEnumerable()
                .SelectAwait(async procInfo =>
                {
                    if (procInfo.CtrlProcId != null)
                    {
                        var baseCtrlProcInfo = await iBaseCtrlProcRepository.GetCacheValueAsync(
                            procInfo.CtrlProcId,
                            cancellationToken: cancellationToken
                        );
                        procInfo.CtrlProcZhCn = baseCtrlProcInfo.CtrlProcZhCn;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.BelongCompany))
                    {
                        var chineseKeyValueAsync = await iCompanyRepository.GetCacheValueAsync(
                            procInfo.BelongCompany,
                            cancellationToken: cancellationToken
                        );
                        procInfo.BelongCompanyValue = chineseKeyValueAsync?.CompanyNameCn;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.ManageCompany))
                    {
                        var chineseKeyValueAsync = await iCompanyRepository.GetCacheValueAsync(
                            procInfo.ManageCompany,
                            cancellationToken: cancellationToken
                        );
                        procInfo.ManageCompanyValue = chineseKeyValueAsync?.CompanyNameCn;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.UndertakeUser))
                    {
                        var chineseKeyValueAsync = await userInfoRepository.GetTextValueAsync(
                            procInfo.UndertakeUser
                        );
                        procInfo.UndertakeUserValue = chineseKeyValueAsync;
                    }

                    if (!string.IsNullOrWhiteSpace(procInfo.CustomerId))
                    {
                        var customer = cusCustomers.FirstOrDefault(it =>
                            it.CustomerId == procInfo.CustomerId
                        );
                        procInfo.IsExclusive = customer?.IsExclusive;
                        procInfo.CustomerName = customer?.CustomerName;
                    }
                    return procInfo;
                })
                .ToListAsync(cancellationToken);
            var asyncDuplexStreamingCall = notificationClient.NotifyAsync();
            var readTask = Task.Factory.StartNew(async () =>
            {
                await foreach (
                    var response in asyncDuplexStreamingCall.ResponseStream.ReadAllAsync(
                        cancellationToken: cancellationToken
                    )
                )
                {
                    if (response is null)
                        continue;

                    var messageId = response.Data.UnpackToString();
                    if (response.Success)
                    {
                        logger.LogInformation(
                            "追踪Id：{TraceId}。邮件Id：{MessageId}。信息：{Message}",
                            response.TraceId,
                            messageId,
                            response.Message
                        );
                    }
                    else
                    {
                        logger.LogError(
                            "追踪Id：{TraceId}。邮件Id：{MessageId}。信息：{Message}",
                            response.TraceId,
                            messageId,
                            response.Message
                        );
                    }
                }
            });

            var dictionary = new Dictionary<string, object>()
            {
                ["期限时间逻辑异常"] = dueTimeErrorWarningDtos,
            };
            var notificationMail = new NotificationMail
            {
                Sender = "【正式版本】系统邮箱",
                MessageId = Guid.NewGuid().ToString(),
                BodyTemplate = BodyTemplate.DeadlineLogicalException,
            };
            using (var stream = new MemoryStream())
            {
                await stream.SaveAsAsync(
                    dictionary,
                    excelType: ExcelType.XLSX,
                    cancellationToken: cancellationToken
                );
                stream.Seek(0, SeekOrigin.Begin);

                notificationMail.Attachments.Add(
                    new MailAttachment
                    {
                        FileName = "期限时间逻辑异常.xlsx",
                        Data = await ByteString.FromStreamAsync(stream, cancellationToken),
                    }
                );
                if (hostEnvironment.IsProduction())
                {
                    notificationMail.ReceiverAccounts.AddRange(
                        new List<MailReceiverAccount>()
                        {
                            new()
                            {
                                DisplayName = "流程部事业邮箱",
                                MailBoxAddress = "<EMAIL>",
                            },
                            new()
                            {
                                DisplayName = "华进国内专利流程",
                                MailBoxAddress = "<EMAIL>",
                            },
                        }
                    );
                }
                else
                {
                    notificationMail.Receivers.AddRange(
                        new List<string>()
                        {
                            "ea937da8-85fe-498d-9843-17e7d616c692",
                            "c1597c51-781b-4682-81c7-e01dc23431ce",
                        }
                    );
                }

                logger.LogInformation(
                    notificationMail.ToJson() + notificationMail.Receivers.ToJson()
                );

                await asyncDuplexStreamingCall.RequestStream.WriteAsync(
                    notificationMail,
                    cancellationToken
                );
            }
            await asyncDuplexStreamingCall.RequestStream.CompleteAsync();

            await readTask;
            return dueTimeErrorWarningDtos;
        }
    }
}
