﻿using Confluent.Kafka;
using Microsoft.Extensions.Options;

namespace iPlatformExtension.Common.MQ.MailMQ
{
    public class KafkaProducerService<TKey, TValue> : IKafkaProducerService
    {
        private readonly KafkaProducerSettings _config;
        private readonly KafkaProducerFactory<TKey, TValue> _producerFactory;
        private IProducer<TKey, TValue> _producer;
        public KafkaProducerService(KafkaProducerFactory<TKey, TValue> producerFactory, IOptions<KafkaProducerSettings> config)
        {
            _producerFactory = producerFactory;
            _config = config.Value;
        }

        public async Task<DeliveryResult<TKey, TValue>> SendMessageAsync(TValue content, CancellationToken cancellationToken = default)
        {
            try
            {
                var topicPartition = new TopicPartition(_config.Topic, new Partition(0));
                _producer ??= _producerFactory.CreateProducer();
                var res = await _producer.ProduceAsync(topicPartition,
                    new Message<TKey, TValue>() { Value = content }, cancellationToken);
                Console.WriteLine($"Delivered '{res.Value}' to '{res.TopicPartitionOffset}'");
                return res;
            }
            catch (ProduceException<Null, string> e)
            {
                Console.WriteLine($"Delivery failed: {e.Error.Reason}");
            }
            return new DeliveryResult<TKey, TValue>();
        }
    }
}
