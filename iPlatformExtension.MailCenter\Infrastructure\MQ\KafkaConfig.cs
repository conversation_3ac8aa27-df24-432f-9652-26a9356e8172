﻿using Confluent.Kafka;
using iPlatformExtension.Common.MQ.KafKa.Converters;
using iPlatformExtension.Common.MQ.MailMQ;
using iPlatformExtension.Model.Dto.MailCenter;


namespace iPlatformExtension.MailCenter.Infrastructure.MQ
{
    public static class KafkaConfig
    {
        public static void InitKafaService(this IServiceCollection services, IConfiguration configuration)
        {
            // 添加Kafka配置
            services.Configure<KafkaProducerSettings>(configuration.GetSection("KafKa:MailCenterProducer"));
            services.Configure<KafkaConsumerSettings>(configuration.GetSection("KafKa:MailCenterConsumer"));
            services.AddSingleton<KafkaProducerFactory<Null, MailCenterMessageContent>>();
            services.AddSingleton<KafkaConsumerFactory<Null, MailCenterMessageContent>>();
            services.AddSingleton<KafkaProducerService<Null, MailCenterMessageContent>>();
            services.AddSingleton<KafkaConsumerService<Null, MailCenterMessageContent>>();
        }
    }
}
