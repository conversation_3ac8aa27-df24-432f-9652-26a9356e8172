﻿using iPlatformExtension.Common.Mediator.Commands;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Commands.AnalysisRule;

/// <summary>
/// 分拣动作
/// </summary>
/// <param name="MailId">邮件id</param>
/// <param name="DisplayName">分拣意见</param>
/// <param name="UndertakeUserId">处理人id</param>
/// <param name="MailType">动作,Send:发件,Receive:收件</param>
/// <returns></returns>
public record SortingCommand(List<string> MailId, string? DisplayName, string? UndertakeUserId,string? MailType = "Receive")
    : IRequest,
        IUnitOfWorkCommandMysql;
