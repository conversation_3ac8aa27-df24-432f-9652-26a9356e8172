﻿using Microsoft.Identity.Client;

namespace iPlatformExtension.MailService.Applications.Models
{
    /// <summary>
    /// 收件用户类型
    /// </summary>
    public static class AddressTypeEnum
    {
        /// <summary>
        /// 收件人
        /// </summary>
        public const string To = "to";

        /// <summary>
        /// 抄送
        /// </summary>
        public const string Cc = "cc";

        /// <summary>
        /// 发件人
        /// </summary>
        public const string From = "from";
    }
}
