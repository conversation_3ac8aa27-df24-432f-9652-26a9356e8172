using iPlatformExtension.Common.Mediator.Commands;
using iPlatformExtension.MailService.Applications.Models;
using MediatR;

namespace iPlatformExtension.MailService.Applications.Commands
{
    /// <summary>
    /// 通过IMAP发送邮件的命令
    /// </summary>
    public record SendMailBySmtpCommand(
        List<string> ToEmails, 
        List<string>? CcEmails, 
        List<string>? BccEmails, 
        string Subject, 
        string Body, 
        bool IsHtml = false,
        List<string>? AttachmentPaths = null,
        MailConfigDto? ConfigDto = null
    ) : IRequest<bool>, IUnitOfWorkCommandMysql;
} 