﻿﻿using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.Signature;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.AspNetCore.Http;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Signature
{
    /// <summary>
    /// 保存签名命令处理器
    /// </summary>
    internal sealed class SaveSignatureCommandHandler(
        IMailSignatureRepository mailSignatureRepository,
        IHttpContextAccessor httpContextAccessor
    ) : IRequestHandler<SaveSignatureCommand, string>
    {
        public async Task<string> Handle(
            SaveSignatureCommand request,
            CancellationToken cancellationToken
        )
        {
            // 获取当前用户ID
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var now = DateTime.Now;

            // 判断是新增还是更新
            if (string.IsNullOrEmpty(request.Id))
            {
                // 新增签名
                var signature = new MailSignature
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = request.Name,
                    Content = request.Content,
                    UserId = userId,
                    CreateBy = userId,
                    CreateTime = now,
                    UpdateBy = userId,
                    UpdateTime = now
                };

                // 插入数据库
                await mailSignatureRepository.InsertAsync(signature, cancellationToken);
                return signature.Id;
            }
            else
            {
                // 更新签名
                // 先查询是否存在
                var signature = await mailSignatureRepository
                    .Where(it => it.Id == request.Id && it.UserId == userId)
                    .FirstAsync(cancellationToken);

                if (signature == null)
                {
                    throw new Exception("签名不存在或无权限修改");
                }

                // 更新签名
                signature.Name = request.Name;
                signature.Content = request.Content;
                signature.UpdateBy = userId;
                signature.UpdateTime = now;

                await mailSignatureRepository.UpdateAsync(signature, cancellationToken);
                return request.Id;
            }
        }
    }
}
