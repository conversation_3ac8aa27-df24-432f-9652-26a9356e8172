﻿using Confluent.Kafka;
using iPlatformExtension.Common.MQ.KafKa.Converters;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;

namespace iPlatformExtension.Common.MQ.MailMQ
{
    public class KafkaProducerFactory<TKey, TValue>
    {
        private readonly KafkaProducerSettings _config;
        private readonly ILogger<KafkaProducerFactory<TKey, TValue>> _logger;

        public KafkaProducerFactory(IOptions<KafkaProducerSettings> config, ILogger<KafkaProducerFactory<TKey, TValue>> logger)
        {
            _config = config.Value;
            _logger = logger;
        }

        public IProducer<TKey, TValue> CreateProducer()
        {
            var producerConfig = new ProducerConfig
            {
                BootstrapServers = _config.BootstrapServers,
                //ClientId = _config.ClientId
                EnableIdempotence = _config.EnableIdempotence,
            };
            var jsonSerializerOptions = new JsonSerializerOptions(JsonSerializerDefaults.Web);
            var builder = new ProducerBuilder<TKey, TValue>(producerConfig)
                 // .SetKeySerializer(new CustomStringSerializer<TKey>(jsonSerializerOptions))
                 .SetValueSerializer(new ValueByUtf8Converter<TValue>());

            builder.SetLogHandler((_, m) =>
            {
                _logger.Log((LogLevel)m.LevelAs(LogLevelType.MicrosoftExtensionsLogging), $"KafkaProducerMessage: {m?.Message}");
            });
            return builder.Build();
        }
    }
}
