using AutoMapper;
using Google.Protobuf;
using Grpc.Core;
using iPlatformExtension.Common.Extensions;
using iPlatformExtension.Common.GrpcServices;
using iPlatformExtension.Messages.Mails;
using iPlatformExtension.Model.BaseModel;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Public.Applications.Models.Statistics;
using iPlatformExtension.Public.Applications.Queries.Statistics;
using MediatR;
using MiniExcelLibs;
using MongoDB.Bson;

namespace iPlatformExtension.Public.Applications.Handlers.Statistics
{
    /// <summary>
    /// 4.内部期限超过7天汇总表
    /// </summary>
    internal sealed class InternalDeadlineExpiredDaysQueryHandler(
        IFreeSql freeSql,
        IMediator mediator,
        Notification.NotificationClient notificationClient,
        IMapper mapper,
        IHostEnvironment hostEnvironment,
        ILogger<InternalDeadlineExpiredDaysQuery> logger
    )
        : IRequestHandler<
            InternalDeadlineExpiredDaysQuery,
            IEnumerable<InternalDeadlineExpiredDaysDto>
        >
    {
        //private static readonly string[] dateLineTeamRoles = ["date_line_team"];

        public async Task<IEnumerable<InternalDeadlineExpiredDaysDto>> Handle(
            InternalDeadlineExpiredDaysQuery request,
            CancellationToken cancellationToken
        )
        {
            var deptRoleCode = SysEnum.DeptRoleCode.DR.ToString();
            string[] dateLineTeamRoles = ["date_line_team"];
            // 先查询具有date_line_team角色的用户ID
            var dateLineTeamUserIds = await freeSql
                .Select<SysUserRole, SysRoleInfo>()
                .WithLock()
                .LeftJoin(ur => ur.t1.RoleId == ur.t2.RoleId)
                .Where(ur => dateLineTeamRoles.Contains(ur.t2.RoleCode))
                .ToListAsync(ur => ur.t1.UserId, cancellationToken);

            //专利质管部与流程要看全部
            var sysDeptUsers = await freeSql
                .Select<SysDeptUser, SysDeptRole, SysUserInfo, SysDeptInfo>()
                .WithLock()
                .LeftJoin(it => it.t1.RoleId == it.t2.RoleId)
                .LeftJoin(it => it.t1.UserId == it.t3.UserId)
                .LeftJoin(it => it.t1.DeptId == it.t4.DeptId)
                .Where(it =>
                    (it.t2.RoleCode == deptRoleCode && it.t1.IsDefault) || it.t1.IsDefault == false
                )
                .Where(it => dateLineTeamUserIds.Contains(it.t3.UserId))
                .Where(it => it.t4.IsEnabled)
                .WithTempQuery(it => new { it.t1.UserId, it.t1.DeptId })
                .UnionAll(
                    freeSql
                        .Select<SysDeptUser, SysUserInfo, SysDeptInfo>()
                        .WithLock()
                        .Distinct()
                        .LeftJoin(it => it.t1.UserId == it.t2.UserId)
                        .LeftJoin(it => it.t1.DeptId == it.t3.DeptId)
                        .Where(it =>
                            it.t2.IsEnabled
                            && it.t3.IsEnabled
                            && it.t3.FullName.Contains(
                                "华进联合, 专利事业群, 专利质量管理部"
                            )
                        )
                        .WithTempQuery(it => new { it.t1.UserId })
                        .FromQuery(freeSql.Select<SysDeptInfo>().Where(it => it.IsEnabled))
                        .LeftJoin(it => true)
                        .WithTempQuery(it => new { it.t1.UserId, it.t2.DeptId })
                )
                .Distinct()
                .WhereIf(request.UserId is not null, it => request.UserId!.Contains(it.UserId))
                .ToListAsync(cancellationToken);

            var intFinishOverDate = await mediator.Send(
                new OverDateWarningQuery(SysEnum.ReportType.IntFinishDate, -7),
                cancellationToken
            );
            var intFirstOverDate = await mediator.Send(
                new OverDateWarningQuery(SysEnum.ReportType.IntFirstDate, -7),
                cancellationToken
            );
            var intFinishOverDates = mapper.Map<List<IntFinishOverDate>>(intFinishOverDate);
            var intFirstOverDates = mapper.Map<List<IntFirstOverDate>>(intFirstOverDate);
            var asyncDuplexStreamingCall = notificationClient.NotifyAsync(cancellationToken: cancellationToken);
            var readTask = Task.Factory.StartNew(
                async () =>
                {
                    await foreach (
                        var response in asyncDuplexStreamingCall.ResponseStream.ReadAllAsync()
                    )
                    {
                        if (response is null)
                            continue;

                        var messageId = response.Data.UnpackToString();
                        if (response.Success)
                        {
                            logger.LogInformation(
                                "追踪Id：{TraceId}。邮件Id：{MessageId}。信息：{Message}",
                                response.TraceId,
                                messageId,
                                response.Message
                            );
                        }
                        else
                        {
                            logger.LogError(
                                "追踪Id：{TraceId}。邮件Id：{MessageId}。信息：{Message}",
                                response.TraceId,
                                messageId,
                                response.Message
                            );
                        }
                    }
                },
                cancellationToken
            );

            foreach (var underTakeUser in sysDeptUsers.Select(it => it.UserId).Distinct())
            {
                var ownDeptList = sysDeptUsers
                    .Where(it => it.UserId == underTakeUser)
                    .Select(it => it.DeptId);
                if (
                    !intFirstOverDates.Any(it => ownDeptList.Contains(it.DeptId))
                    && !intFinishOverDates.Any(it => ownDeptList.Contains(it.DeptId))
                )
                    continue;

                var dictionary = new Dictionary<string, object>()
                {
                    ["初稿期限(内)"] = intFirstOverDates.Where(it =>
                        ownDeptList.Contains(it.DeptId)
                    ),
                    ["定稿期限(内)"] = intFinishOverDates.Where(it =>
                        ownDeptList.Contains(it.DeptId)
                    ),
                };

                var notificationMail = new NotificationMail
                {
                    Sender = "【正式版本】系统邮箱",
                    MessageId = Guid.NewGuid().ToString(),
                    BodyTemplate = BodyTemplate.InternalDeadlineExpiredDays,
                };
                using var stream = new MemoryStream();
                await stream.SaveAsAsync(
                    dictionary,
                    excelType: ExcelType.XLSX,
                    cancellationToken: cancellationToken
                );
                stream.Seek(0, SeekOrigin.Begin);

                notificationMail.Attachments.Add(
                    new MailAttachment
                    {
                        FileName = "内部期限超过7天汇总表.xlsx",
                        Data = await ByteString.FromStreamAsync(stream, cancellationToken),
                    }
                );
                var receiverIds = hostEnvironment.IsProduction()
                    ? [underTakeUser]
                    : new List<string>
                    {
                        "ea937da8-85fe-498d-9843-17e7d616c692",
                        "c1597c51-781b-4682-81c7-e01dc23431ce",
                    };
                notificationMail.Receivers.AddRange(receiverIds);
                logger.LogInformation(
                    "发送通知邮件: {NotificationMail}, 接收者: {Receivers}",
                    notificationMail.ToJson(),
                    notificationMail.Receivers.ToJson()
                );

                await asyncDuplexStreamingCall.RequestStream.WriteAsync(
                    notificationMail,
                    cancellationToken
                );
            }

            var mail = new NotificationMail
            {
                Sender = "【正式版本】系统邮箱",
                MessageId = Guid.NewGuid().ToString(),
                BodyTemplate = BodyTemplate.InternalDeadlineExpiredDays,
            };
            using (var stream = new MemoryStream())
            {
                await stream.SaveAsAsync(
                    new Dictionary<string, object>()
                    {
                        ["初稿期限(内)"] = intFirstOverDates,
                        ["定稿期限(内)"] = intFinishOverDates,
                    },
                    excelType: ExcelType.XLSX,
                    cancellationToken: cancellationToken
                );
                stream.Seek(0, SeekOrigin.Begin);

                mail.Attachments.Add(
                    new MailAttachment
                    {
                        FileName = "内部期限超过7天汇总表.xlsx",
                        Data = await ByteString.FromStreamAsync(stream, cancellationToken),
                    }
                );
                if (hostEnvironment.IsProduction())
                {
                    mail.ReceiverAccounts.AddRange([
                        new()
                        {
                            DisplayName = "流程部事业邮箱",
                            MailBoxAddress = "<EMAIL>",
                        }
                    ]);
                }
                else
                {
                    mail.Receivers.AddRange([
                        "ea937da8-85fe-498d-9843-17e7d616c692",
                        "c1597c51-781b-4682-81c7-e01dc23431ce"
                    ]);
                }
                await asyncDuplexStreamingCall.RequestStream.WriteAsync(mail, cancellationToken);
            }

            await asyncDuplexStreamingCall.RequestStream.CompleteAsync();

            await readTask;
            return [];
        }
    }
}
