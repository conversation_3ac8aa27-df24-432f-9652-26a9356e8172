﻿using Confluent.Kafka;
using FreeSql;
using iPlatformExtension.Common.Db.FreeSQL;
using iPlatformExtension.Common.MQ.MailMQ;
using iPlatformExtension.MailService.Applications.Commands;
using iPlatformExtension.MailService.Applications.Queries;
using iPlatformExtension.MailService.Infrastructure.MQ;
using iPlatformExtension.Model.Dto.MailCenter;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using static iPlatformExtension.Model.Enum.SysEnum;

namespace iPlatformExtension.MailService.Applications.Handlers
{
    public class LoadMailSendHandler(IFreeSql<MailCenterFreeSql> freeSql,
         KafkaProducerService<Null, MailServerMessageContent> producer
        ) : IRequestHandler<MailSendQuery>
    {
        public async Task Handle(MailSendQuery request, CancellationToken cancellationToken)
        {
            var lst = freeSql.Select<MailSendList>().WithLock().Where(o => o.Status == 1 && (SqlExt.IsNull(o.SendTime, DateTime.Now) >= DateTime.Now)).ToList();

            foreach (var item in lst)
            {
                var mailInfo = await freeSql.Select<MailSend>().Where(o => o.MailId == item.MailId).ToOneAsync(cancellationToken);
                if (mailInfo != null && mailInfo.Status == 1)
                {
                    //判断是否存在缓存


                    //进入队列
                    var res = await producer.SendMessageAsync(new MailServerMessageContent()
                    {
                        MailId = mailInfo.MailId,
                        MailTitle = mailInfo.MailSubject,
                        MailNo = mailInfo.MailNo,
                        OperatorUser = "测试",
                        DateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        RecipientBy = ""
                    }, cancellationToken);

                    //item.Status = SendStatusType.Sending.GetHashCode();
                    //mailInfo.Status = SendStatusType.Sending.GetHashCode();
                    //记录缓存
                }
            }
            return;

        }
    }
}
