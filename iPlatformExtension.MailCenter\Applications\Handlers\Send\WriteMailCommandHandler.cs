using iPlatformExtension.Common.Extensions;
using iPlatformExtension.MailCenter.Applications.Commands.Send;
using iPlatformExtension.MailCenterRepository.Interface;
using iPlatformExtension.Model.Enum;
using iPlatformExtension.Model.MailCenter;
using MediatR;
using Microsoft.AspNetCore.Http;

namespace iPlatformExtension.MailCenter.Applications.Handlers.Send;

/// <summary>
/// 写邮件命令处理程序
/// </summary>
internal sealed class WriteMailCommandHandler(
        IMailSendRepository mailSendRepository,
        IMailUserRepository mailUserRepository,
        IHttpContextAccessor httpContextAccessor) : IRequestHandler<WriteMailCommand, string>
    {
        public async Task<string> Handle(WriteMailCommand request, CancellationToken cancellationToken)
        {
            // 获取当前用户ID
            var userId = httpContextAccessor.HttpContext?.User.GetUserID() ?? string.Empty;
            var now = DateTime.Now;

            // 确定邮件状态
            int mailStatus = SysEnum.SendStatusType.Draft.GetHashCode(); // 默认为草稿状态


            string mailId;

            if (string.IsNullOrEmpty(request.MailId))
            {
                // 新增邮件
                mailId = Guid.NewGuid().ToString();

                // 创建邮件主体
                var mailSend = new MailSend
                {
                    MailId = mailId,
                    MailSubject = request.MailSubject,
                    MailHtmlBody = request.MailHtmlBody,
                    MailRelayBody = request.MailRelayBody ?? "",  // 添加转发邮件正文
                    HostId = request.HostId,
                    IsImportant = request.IsImportant,
                    IsRead = request.IsRead,
                    MailDate = now,
                    Status = mailStatus,
                    Attachments = request.Attachments?.Count ?? 0,
                    SignatureBody = request.SignatureBody ?? ""
                };

                // 保存邮件主体
                await mailSendRepository.InsertAsync(mailSend, cancellationToken);

                // 同步 MailTo、MailCc 和 MailBcc 到 mail_user 表
                await SyncMailUsersAsync(mailId, request.MailTo, request.MailCc, request.MailBcc, cancellationToken);

            }
            else
            {
                // 编辑现有邮件
                mailId = request.MailId;

                // 获取现有邮件
                var existingMail = await mailSendRepository.Where(m => m.MailId == mailId)
                    .FirstAsync(cancellationToken);

                ArgumentNullException.ThrowIfNull(existingMail, nameof(existingMail));


                // 更新邮件主体
                existingMail.MailSubject = request.MailSubject;
                existingMail.MailHtmlBody = request.MailHtmlBody;
                existingMail.MailRelayBody = request.MailRelayBody ?? "";  // 更新转发邮件正文
                existingMail.HostId = request.HostId;
                existingMail.IsImportant = request.IsImportant;
                existingMail.IsRead = request.IsRead;
                existingMail.Status = mailStatus;
                existingMail.Attachments = request.Attachments?.Count ?? 0;
                existingMail.SignatureBody = request.SignatureBody ?? "";

                // 保存更新后的邮件主体
                await mailSendRepository.UpdateAsync(existingMail, cancellationToken);

                // 同步 MailTo、MailCc 和 MailBcc 到 mail_user 表
                await SyncMailUsersAsync(mailId, request.MailTo, request.MailCc, request.MailBcc, cancellationToken);

            }

            // 返回邮件ID
            return mailId;
        }

        /// <summary>
        /// 同步邮件收件人、抄送人和密送人到 mail_user 表
        /// </summary>
        /// <param name="mailId">邮件ID</param>
        /// <param name="mailTo">收件人列表</param>
        /// <param name="mailCc">抄送人列表</param>
        /// <param name="mailBcc">密送人列表</param>
        /// <param name="cancellationToken">取消令牌</param>
        private async Task SyncMailUsersAsync(string mailId, List<MailAddressList> mailTo, List<MailAddressList>? mailCc, List<MailAddressList>? mailBcc, CancellationToken cancellationToken)
        {
            // 删除现有的收件人、抄送人和密送人记录
            await mailUserRepository.DeleteAsync(u => u.MailId == mailId && (u.AddressType == "to" || u.AddressType == "cc" || u.AddressType == "bcc"), cancellationToken);

            var mailUsers = new List<MailUser>();

            // 处理收件人
            if (mailTo != null && mailTo.Count > 0)
            {
                mailUsers.AddRange(ParseEmailAddresses(mailId, mailTo, "to"));
            }

            // 处理抄送人
            if (mailCc != null && mailCc.Count > 0)
            {
                mailUsers.AddRange(ParseEmailAddresses(mailId, mailCc, "cc"));
            }

            // 处理密送人
            if (mailBcc != null && mailBcc.Count > 0)
            {
                mailUsers.AddRange(ParseEmailAddresses(mailId, mailBcc, "bcc"));
            }

            // 批量插入邮件用户记录
            if (mailUsers.Count > 0)
            {
                await mailUserRepository.InsertAsync(mailUsers, cancellationToken);
            }
        }

        /// <summary>
        /// 解析邮箱地址列表
        /// </summary>
        /// <param name="mailId">邮件ID</param>
        /// <param name="addressList">邮箱地址列表</param>
        /// <param name="addressType">地址类型：to-收件人，cc-抄送，bcc-密送</param>
        /// <returns>解析后的邮件用户列表</returns>
        private static List<MailUser> ParseEmailAddresses(string mailId, List<MailAddressList> addressList, string addressType)
        {
            var result = new List<MailUser>();

            for (int i = 0; i < addressList.Count; i++)
            {
                var address = addressList[i];
                if (address == null || string.IsNullOrWhiteSpace(address.MailAddress))
                    continue;

                string displayName = address.DisplayName ?? address.MailAddress;
                string mailAddress = address.MailAddress.Trim();

                // 验证邮箱地址格式
                if (!string.IsNullOrEmpty(mailAddress) && mailAddress.Contains('@'))
                {
                    result.Add(new MailUser
                    {
                        Id = Guid.NewGuid().ToString(),
                        MailId = mailId,
                        AddressType = addressType,
                        DisplayName = displayName,
                        MailAddress = mailAddress,
                        Seq = i + 1
                    });
                }
            }

            return result;
        }

    }
