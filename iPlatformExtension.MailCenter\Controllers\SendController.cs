﻿using iPlatformExtension.MailCenter.Applications.Commands.Send;
using iPlatformExtension.MailCenter.Applications.Queries.Receive;
using iPlatformExtension.MailCenter.Applications.Queries.Send;
using iPlatformExtension.Model.Dto;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace iPlatformExtension.MailCenter.Controllers
{
    /// <summary>
    /// 发送邮件控制器
    /// </summary>
    /// <param name="mediator"></param>
    [Route("[controller]")]
    [ApiController]
    public class SendController(IMediator mediator) : ControllerBase
    {
        /// <summary>
        /// 作废邮件
        /// </summary>
        /// <remarks>
        /// 作废邮件接口，将邮件状态修改为已作废，并删除所有阅读人和关联数据和流程。
        /// 注意：只有发送时间不超过2分钟的邮件才能作废。
        /// </remarks>
        /// <param name="command">作废邮件命令</param>
        /// <returns></returns>
        [HttpPost("DiscardMail")]
        public async Task DiscardMail([FromBody] DiscardMailCommand command)
        {
            await mediator.Send(command);
        }

        /// <summary>
        /// 删除邮件
        /// </summary>
        /// <remarks>
        /// 删除邮件接口，完全删除邮件及其相关数据。
        /// 注意：只有处于草稿状态或非结束流程节点的邮件才能删除。
        /// </remarks>
        /// <param name="command">删除邮件命令</param>
        /// <returns></returns>
        [HttpPost("DeleteMail")]
        public async Task DeleteMail([FromBody] DeleteMailCommand command)
        {
            await mediator.Send(command);
        }

        /// <summary>
        /// 写邮件
        /// </summary>
        /// <remarks>
        /// 写邮件接口，用于创建或编辑邮件。
        /// 如果不提供邮件ID，则创建新邮件；如果提供邮件ID，则编辑现有邮件。
        /// 默认情况下，邮件会保存为草稿状态。
        /// 如果提供了发送时间，则邮件会设置为定时发送状态。
        /// </remarks>
        /// <param name="command">写邮件命令</param>
        /// <returns>邮件ID</returns>
        [HttpPost("WriteMail")]
        public async Task<ActionResult<string>> WriteMail([FromBody] WriteMailCommand command)
        {
            var mailId = await mediator.Send(command);
            return Ok(mailId);
        }

        /// <summary>
        /// 上传邮件附件
        /// </summary>
        /// <remarks>
        /// 上传邮件附件接口，用于上传邮件附件并将文件信息保存到mail_attachments表。
        /// 如果提供了邮件ID，则附件会与该邮件关联；如果没有提供邮件ID，则附件会保存为临时附件。
        /// 文件会上传到华为云OBS存储，并返回附件的相关信息。
        /// </remarks>
        /// <param name="mailId">邮件ID，可选，如果为空则表示临时附件</param>
        /// <param name="file">上传的文件</param>
        /// <returns>附件信息</returns>
        [HttpPost("UploadAttachment")]
        public async Task<ActionResult<UploadMailAttachmentResult>> UploadAttachment([FromForm] string? mailId, [FromForm] IFormFile file)
        {
            var command = new UploadMailAttachmentCommand(mailId, file);
            var result = await mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// 预览邮件
        /// </summary>
        /// <remarks>
        /// 预览邮件接口，获取邮件内容并处理HTML正文。
        /// 处理包括：
        /// 1. 限制图片大小，防止溢出
        /// 2. 使链接在新窗口打开
        /// 3. 确保表格响应式显示
        /// 4. 添加预览样式
        /// </remarks>
        /// <param name="mailId">邮件ID</param>
        /// <returns>预览邮件结果</returns>
        [HttpGet("PreviewMail/{mailId}")]
        public async Task<ActionResult<PreviewMailResult>> PreviewMail(string mailId)
        {
            var query = new PreviewMailQuery(mailId);
            var result = await mediator.Send(query);
            return Ok(result);
        }

        
        /// <summary>
        /// 获取邮件列表
        /// </summary>
        /// <remarks>
        /// 获取邮件列表接口，支持多种搜索条件：
        /// - 文件编号
        /// - 邮件主题
        /// - 发件人
        /// - 日期范围
        /// 默认查询草稿状态的邮件。
        /// </remarks>
        /// <param name="query">查询参数</param>
        /// <returns>邮件列表分页结果</returns>
        [HttpPost("GetMailList")]
        public async Task<ActionResult<PageResult<GetMailListDto>>> GetMailList([FromBody] GetMailListQuery query)
        {
            var result = await mediator.Send(query);
            return Ok(result);
        }

        /// <summary>
        /// 退回草稿
        /// </summary>
        /// <remarks>
        /// 退回草稿接口，将邮件状态修改为草稿状态。
        /// 注意：只有发送时间不超过2分钟的邮件才能退回草稿。
        /// 退回后将清除相关流程数据。
        /// </remarks>
        /// <param name="command">退回草稿命令</param>
        /// <returns></returns>
        [HttpPost("ReturnToDraft")]
        public async Task ReturnToDraft([FromBody] ReturnToDraftCommand command)
        {
            await mediator.Send(command);
        }
    }
}
