﻿using Confluent.Kafka;
using iPlatformExtension.Common.MQ.KafKa.Converters;
using iPlatformExtension.Common.MQ.MailMQ;
using iPlatformExtension.Model.Dto.MailCenter;


namespace iPlatformExtension.MailService.Infrastructure.MQ
{
    public static class KafkaConfig
    {
        public static void InitKafaService(this IServiceCollection services, IConfiguration configuration)
        {
            // 添加Kafka配置
            services.Configure<KafkaProducerSettings>(configuration.GetSection("KafKa:MailServerProducer"));
            services.Configure<KafkaConsumerSettings>(configuration.GetSection("KafKa:MailServerConsumer"));
            services.AddSingleton<KafkaProducerFactory<Null, MailServerMessageContent>>();
            services.AddSingleton<KafkaConsumerFactory<Null, MailServerMessageContent>>();
            services.AddSingleton<KafkaConsumerService<Null, MailServerMessageContent>>();
            services.AddSingleton<KafkaProducerService<Null, MailServerMessageContent>>();
        }
    }
}
