#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
USER app
WORKDIR /app
EXPOSE 5079
ENV TZ=Asia/Shanghai
ENV ASPNETCORE_ENVIRONMENT=Staging
ENV ASPNETCORE_URLS=http://+:5079

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Staging
WORKDIR /src
COPY ["NuGet.config", "."]
COPY ["iPlatformExtension.MailService/iPlatformExtension.MailService.csproj", "iPlatformExtension.MailService/"]
COPY ["iPlatformExtension.Common/iPlatformExtension.Common.csproj", "iPlatformExtension.Common/"]
COPY ["iPlatformExtension.Model/iPlatformExtension.Model.csproj", "iPlatformExtension.Model/"]
RUN dotnet restore "./iPlatformExtension.MailService/./iPlatformExtension.MailService.csproj"
COPY . .
WORKDIR "/src/iPlatformExtension.MailService"
RUN dotnet build "./iPlatformExtension.MailService.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./iPlatformExtension.MailService.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "iPlatformExtension.MailService.dll"]