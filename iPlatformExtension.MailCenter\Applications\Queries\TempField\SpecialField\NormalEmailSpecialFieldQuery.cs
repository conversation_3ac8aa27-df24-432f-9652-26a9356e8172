using iPlatformExtension.MailCenter.Applications.Models.Template;
using MediatR;

namespace iPlatformExtension.MailCenter.Applications.Queries.TempField.SpecialField;

/// <summary>
/// normal_email特殊字段查询
/// </summary>
public record NormalEmailSpecialFieldQuery(
    Model.MailCenter.TempField Field,
    List<MailDataGroup>? DataGroups = null
) : ISpecialFieldQuery, IRequest<Dictionary<string, string>>;